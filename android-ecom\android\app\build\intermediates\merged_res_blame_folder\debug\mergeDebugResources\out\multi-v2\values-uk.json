{"logs": [{"outputFile": "com.sohangpurh53.ecommtest.app-mergeDebugResources-62:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6e705899487cb68772be9da92e21a0c\\transformed\\browser-1.6.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4662,5310,5417,5537", "endColumns": "109,106,119,107", "endOffsets": "4767,5412,5532,5640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65209923ef4068f1ffed3bedec62176\\transformed\\jetified-ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,995,1083,1155,1231,1308,1385,1465,1535", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,75,76,76,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,990,1078,1150,1226,1303,1380,1460,1530,1653"}, "to": {"startLines": "47,48,52,54,55,75,76,127,128,132,133,137,141,144,146,151,152,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4292,4385,4772,4967,5068,7204,7286,11303,11391,11729,11814,12137,12452,12688,12838,13248,13328,13478", "endColumns": "92,83,101,100,83,81,88,87,81,84,87,71,75,76,76,79,69,122", "endOffsets": "4380,4464,4869,5063,5147,7281,7370,11386,11468,11809,11897,12204,12523,12760,12910,13323,13393,13596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,816,898,989,1082,1177,1271,1371,1464,1559,1654,1745,1836,1935,2041,2147,2245,2352,2459,2564,2734,2834", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,811,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829,2911"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "379,488,590,698,784,889,1007,1090,1172,1263,1356,1451,1545,1645,1738,1833,1928,2019,2110,2209,2315,2421,2519,2626,2733,2838,3008,12055", "endColumns": "108,101,107,85,104,117,82,81,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "483,585,693,779,884,1002,1085,1167,1258,1351,1446,1540,1640,1733,1828,1923,2014,2105,2204,2310,2416,2514,2621,2728,2833,3003,3103,12132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,329,417,513,629,712,779,870,936,999,1087,1154,1212,1283,1342,1396,1510,1570,1633,1687,1760,1879,1965,2048,2157,2242,2329,2417,2484,2550,2622,2698,2788,2861,2938,3019,3093,3183,3262,3353,3449,3523,3604,3699,3753,3819,3906,3992,4054,4118,4181,4288,4380,4478,4570", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,87,95,115,82,66,90,65,62,87,66,57,70,58,53,113,59,62,53,72,118,85,82,108,84,86,87,66,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,81", "endOffsets": "324,412,508,624,707,774,865,931,994,1082,1149,1207,1278,1337,1391,1505,1565,1628,1682,1755,1874,1960,2043,2152,2237,2324,2412,2479,2545,2617,2693,2783,2856,2933,3014,3088,3178,3257,3348,3444,3518,3599,3694,3748,3814,3901,3987,4049,4113,4176,4283,4375,4473,4565,4647"}, "to": {"startLines": "2,36,44,45,46,56,57,72,77,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3182,3997,4093,4209,5152,5219,6976,7375,7510,7598,7665,7723,7794,7853,7907,8021,8081,8144,8198,8271,8612,8698,8781,8890,8975,9062,9150,9217,9283,9355,9431,9521,9594,9671,9752,9826,9916,9995,10086,10182,10256,10337,10432,10486,10552,10639,10725,10787,10851,10914,11021,11113,11211,11473", "endLines": "7,36,44,45,46,56,57,72,77,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "endColumns": "12,87,95,115,82,66,90,65,62,87,66,57,70,58,53,113,59,62,53,72,118,85,82,108,84,86,87,66,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,81", "endOffsets": "374,3265,4088,4204,4287,5214,5305,7037,7433,7593,7660,7718,7789,7848,7902,8016,8076,8139,8193,8266,8385,8693,8776,8885,8970,9057,9145,9212,9278,9350,9426,9516,9589,9666,9747,9821,9911,9990,10081,10177,10251,10332,10427,10481,10547,10634,10720,10782,10846,10909,11016,11108,11206,11298,11550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09fbd29eeff6f06226720f66e439d483\\transformed\\biometric-1.1.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,255,382,513,654,765,892,1026,1125,1255,1387", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "157,250,377,508,649,760,887,1021,1120,1250,1382,1507"}, "to": {"startLines": "50,53,61,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4555,4874,5645,5772,5903,6044,6155,6282,6416,6515,6645,6777", "endColumns": "106,92,126,130,140,110,126,133,98,129,131,124", "endOffsets": "4657,4962,5767,5898,6039,6150,6277,6411,6510,6640,6772,6897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "37,38,39,40,41,42,43,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3270,3370,3472,3573,3674,3779,3884,13073", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "3365,3467,3568,3669,3774,3879,3992,13169"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,215,289,363,451,523,590,666,745,833,919,991,1072,1157,1233,1315,1398,1475,1548,1621,1706,1780,1860,1930", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "124,210,284,358,446,518,585,661,740,828,914,986,1067,1152,1228,1310,1393,1470,1543,1616,1701,1775,1855,1925,2010"}, "to": {"startLines": "35,49,71,73,74,78,91,92,93,130,131,134,135,138,139,140,142,143,145,147,148,150,153,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3108,4469,6902,7042,7116,7438,8390,8457,8533,11555,11643,11902,11974,12209,12294,12370,12528,12611,12765,12915,12988,13174,13398,13601,13671", "endColumns": "73,85,73,73,87,71,66,75,78,87,85,71,80,84,75,81,82,76,72,72,84,73,79,69,84", "endOffsets": "3177,4550,6971,7111,7199,7505,8452,8528,8607,11638,11724,11969,12050,12289,12365,12447,12606,12683,12833,12983,13068,13243,13473,13666,13751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf233ddec2dc68e89587520b037b44b2\\transformed\\jetified-foundation-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,152", "endColumns": "96,99", "endOffsets": "147,247"}, "to": {"startLines": "157,158", "startColumns": "4,4", "startOffsets": "13756,13853", "endColumns": "96,99", "endOffsets": "13848,13948"}}]}]}