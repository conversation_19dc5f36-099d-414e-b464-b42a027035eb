# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at D:/Triumph/android-ecom/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/android/app/build/generated/autolinking/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/RNDateTimePickerCGen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/Props.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp"
  "D:/Triumph/android-ecom/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp"
  "D:/Triumph/android-ecom/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()

# override_cpp_SRC at D:/Triumph/android-ecom/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:42 (file)
# input_SRC at D:/Triumph/android-ecom/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:47 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "D:/Triumph/android-ecom/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "D:/Triumph/android-ecom/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/Triumph/android-ecom/android/app/.cxx/Debug/n4y6005f/x86/CMakeFiles/cmake.verify_globs")
endif()
