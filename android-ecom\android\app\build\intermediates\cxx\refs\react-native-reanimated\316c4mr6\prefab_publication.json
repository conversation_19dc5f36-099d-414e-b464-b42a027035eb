{"installationFolder": "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "x86", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6e48686v\\obj\\x86\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6e48686v\\x86\\android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "x86", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6e48686v\\obj\\x86\\libworklets.so", "abiAndroidGradleBuildJsonFile": "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6e48686v\\x86\\android_gradle_build.json"}]}]}}