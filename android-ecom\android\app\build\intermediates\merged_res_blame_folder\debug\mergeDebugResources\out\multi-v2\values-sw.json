{"logs": [{"outputFile": "com.sohangpurh53.ecommtest.app-mergeDebugResources-62:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09fbd29eeff6f06226720f66e439d483\\transformed\\biometric-1.1.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,163,262,382,515,649,791,923,1066,1164,1291,1421", "endColumns": "107,98,119,132,133,141,131,142,97,126,129,124", "endOffsets": "158,257,377,510,644,786,918,1061,1159,1286,1416,1541"}, "to": {"startLines": "48,51,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4397,4720,5487,5607,5740,5874,6016,6148,6291,6389,6516,6646", "endColumns": "107,98,119,132,133,141,131,142,97,126,129,124", "endOffsets": "4500,4814,5602,5735,5869,6011,6143,6286,6384,6511,6641,6766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65209923ef4068f1ffed3bedec62176\\transformed\\jetified-ui-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,280,381,482,568,649,750,841,923,1008,1095,1169,1244,1321,1398,1475,1545", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "194,275,376,477,563,644,745,836,918,1003,1090,1164,1239,1316,1393,1470,1540,1661"}, "to": {"startLines": "45,46,50,52,53,73,74,125,126,130,131,135,139,142,144,149,150,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4136,4230,4619,4819,4920,7067,7148,11280,11371,11700,11785,12117,12443,12684,12837,13253,13330,13483", "endColumns": "93,80,100,100,85,80,100,90,81,84,86,73,74,76,76,76,69,120", "endOffsets": "4225,4306,4715,4915,5001,7143,7244,11366,11448,11780,11867,12186,12513,12756,12909,13325,13395,13599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,350,433,502,570,649,734,817,900,972,1062,1152,1231,1314,1398,1480,1556,1632,1719,1794,1877,1952", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "119,205,276,345,428,497,565,644,729,812,895,967,1057,1147,1226,1309,1393,1475,1551,1627,1714,1789,1872,1947,2025"}, "to": {"startLines": "33,47,69,71,72,76,89,90,91,128,129,132,133,136,137,138,140,141,143,145,146,148,151,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2960,4311,6771,6915,6984,7312,8297,8365,8444,11534,11617,11872,11944,12191,12281,12360,12518,12602,12761,12914,12990,13178,13400,13604,13679", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "3024,4392,6837,6979,7062,7376,8360,8439,8524,11612,11695,11939,12029,12276,12355,12438,12597,12679,12832,12985,13072,13248,13478,13674,13752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf233ddec2dc68e89587520b037b44b2\\transformed\\jetified-foundation-release\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "155,156", "startColumns": "4,4", "startOffsets": "13757,13858", "endColumns": "100,102", "endOffsets": "13853,13956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6e705899487cb68772be9da92e21a0c\\transformed\\browser-1.6.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "49,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4505,5166,5267,5384", "endColumns": "113,100,116,102", "endOffsets": "4614,5262,5379,5482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "35,36,37,38,39,40,41,147", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3102,3196,3298,3395,3496,3603,3710,13077", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3191,3293,3390,3491,3598,3705,3820,13173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,367,466,574,664,769,886,969,1051,1142,1235,1330,1424,1524,1617,1712,1806,1897,1988,2070,2171,2279,2378,2485,2597,2701,2863,12034", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "362,461,569,659,764,881,964,1046,1137,1230,1325,1419,1519,1612,1707,1801,1892,1983,2065,2166,2274,2373,2480,2592,2696,2858,2955,12112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,287,386,515,598,666,758,831,894,980,1043,1108,1176,1239,1293,1425,1482,1544,1598,1672,1810,1891,1971,2073,2158,2245,2333,2400,2466,2538,2620,2710,2782,2857,2928,3001,3098,3172,3267,3364,3438,3523,3623,3676,3744,3832,3922,3984,4048,4111,4228,4338,4449,4561", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,101,84,86,87,66,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,80", "endOffsets": "209,282,381,510,593,661,753,826,889,975,1038,1103,1171,1234,1288,1420,1477,1539,1593,1667,1805,1886,1966,2068,2153,2240,2328,2395,2461,2533,2615,2705,2777,2852,2923,2996,3093,3167,3262,3359,3433,3518,3618,3671,3739,3827,3917,3979,4043,4106,4223,4333,4444,4556,4637"}, "to": {"startLines": "2,34,42,43,44,54,55,70,75,77,78,79,80,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3029,3825,3924,4053,5006,5074,6842,7249,7381,7467,7530,7595,7663,7726,7780,7912,7969,8031,8085,8159,8529,8610,8690,8792,8877,8964,9052,9119,9185,9257,9339,9429,9501,9576,9647,9720,9817,9891,9986,10083,10157,10242,10342,10395,10463,10551,10641,10703,10767,10830,10947,11057,11168,11453", "endLines": "5,34,42,43,44,54,55,70,75,77,78,79,80,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "endColumns": "12,72,98,128,82,67,91,72,62,85,62,64,67,62,53,131,56,61,53,73,137,80,79,101,84,86,87,66,65,71,81,89,71,74,70,72,96,73,94,96,73,84,99,52,67,87,89,61,63,62,116,109,110,111,80", "endOffsets": "259,3097,3919,4048,4131,5069,5161,6910,7307,7462,7525,7590,7658,7721,7775,7907,7964,8026,8080,8154,8292,8605,8685,8787,8872,8959,9047,9114,9180,9252,9334,9424,9496,9571,9642,9715,9812,9886,9981,10078,10152,10237,10337,10390,10458,10546,10636,10698,10762,10825,10942,11052,11163,11275,11529"}}]}]}