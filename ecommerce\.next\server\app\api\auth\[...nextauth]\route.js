/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/[...nextauth]/route";
exports.ids = ["app/api/auth/[...nextauth]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   handler: () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_authOptions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../../lib/authOptions */ \"(rsc)/./lib/authOptions.ts\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_1___default()(_lib_authOptions__WEBPACK_IMPORTED_MODULE_0__.authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEQ7QUFDekI7QUFFMUIsTUFBTUUsVUFBVUQsZ0RBQVFBLENBQUNELHlEQUFXQSxFQUFFO0FBRUoiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXGFwcFxcYXBpXFxhdXRoXFxbLi4ubmV4dGF1dGhdXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJy4uLy4uLy4uLy4uL2xpYi9hdXRoT3B0aW9ucyc7XHJcbmltcG9ydCBOZXh0QXV0aCBmcm9tICduZXh0LWF1dGgnO1xyXG5cclxuZXhwb3J0IGNvbnN0IGhhbmRsZXIgPSBOZXh0QXV0aChhdXRoT3B0aW9ucyk7XHJcblxyXG5leHBvcnQgeyBoYW5kbGVyIGFzIEdFVCwgaGFuZGxlciBhcyBQT1NUfSJdLCJuYW1lcyI6WyJhdXRoT3B0aW9ucyIsIk5leHRBdXRoIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./constant/urls.ts":
/*!**************************!*\
  !*** ./constant/urls.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADD_TO_CART: () => (/* binding */ ADD_TO_CART),\n/* harmony export */   ADD_TO_WISHLIST: () => (/* binding */ ADD_TO_WISHLIST),\n/* harmony export */   BRANDS: () => (/* binding */ BRANDS),\n/* harmony export */   CATEGORIES: () => (/* binding */ CATEGORIES),\n/* harmony export */   CATEGORIZE_PRODUCTS: () => (/* binding */ CATEGORIZE_PRODUCTS),\n/* harmony export */   CONTACT_FORM: () => (/* binding */ CONTACT_FORM),\n/* harmony export */   FORGOT_PASSWORD: () => (/* binding */ FORGOT_PASSWORD),\n/* harmony export */   FUTURED_PRODUCTS: () => (/* binding */ FUTURED_PRODUCTS),\n/* harmony export */   GET_PROMO_CODE: () => (/* binding */ GET_PROMO_CODE),\n/* harmony export */   MAIN_URL: () => (/* binding */ MAIN_URL),\n/* harmony export */   ORDERS: () => (/* binding */ ORDERS),\n/* harmony export */   PAYMENTS_PHONEPE_INITIATE: () => (/* binding */ PAYMENTS_PHONEPE_INITIATE),\n/* harmony export */   PRODUCTS: () => (/* binding */ PRODUCTS),\n/* harmony export */   PROFILE_UPDATE: () => (/* binding */ PROFILE_UPDATE),\n/* harmony export */   PROMOCODE_APPLY: () => (/* binding */ PROMOCODE_APPLY),\n/* harmony export */   PUBLIC_ORDER_TRACKING: () => (/* binding */ PUBLIC_ORDER_TRACKING),\n/* harmony export */   RANDOM_PRODUCTS: () => (/* binding */ RANDOM_PRODUCTS),\n/* harmony export */   REMOVE_FROM_WISHLIST: () => (/* binding */ REMOVE_FROM_WISHLIST),\n/* harmony export */   RESET_PASSWORD: () => (/* binding */ RESET_PASSWORD),\n/* harmony export */   SEARCH_BY_TRACKING: () => (/* binding */ SEARCH_BY_TRACKING),\n/* harmony export */   SHIPPING_BULK_TRACK: () => (/* binding */ SHIPPING_BULK_TRACK),\n/* harmony export */   SHIPPING_CALCULATE_RATES: () => (/* binding */ SHIPPING_CALCULATE_RATES),\n/* harmony export */   SHIPPING_HEALTH: () => (/* binding */ SHIPPING_HEALTH),\n/* harmony export */   SHIPPING_METHODS: () => (/* binding */ SHIPPING_METHODS),\n/* harmony export */   SHIPPING_STATUS: () => (/* binding */ SHIPPING_STATUS),\n/* harmony export */   SHIPPING_TRACK_ORDER: () => (/* binding */ SHIPPING_TRACK_ORDER),\n/* harmony export */   SHIPPING_VALIDATE_PINCODE: () => (/* binding */ SHIPPING_VALIDATE_PINCODE),\n/* harmony export */   TOKEN_REFFRESH: () => (/* binding */ TOKEN_REFFRESH),\n/* harmony export */   UPDATE_CART: () => (/* binding */ UPDATE_CART),\n/* harmony export */   USER_ADDRESS: () => (/* binding */ USER_ADDRESS),\n/* harmony export */   USER_CART: () => (/* binding */ USER_CART),\n/* harmony export */   USER_DETAIL: () => (/* binding */ USER_DETAIL),\n/* harmony export */   USER_LOGIN: () => (/* binding */ USER_LOGIN),\n/* harmony export */   USER_LOGOUT: () => (/* binding */ USER_LOGOUT),\n/* harmony export */   USER_REFFRESH_BLACKLIST: () => (/* binding */ USER_REFFRESH_BLACKLIST),\n/* harmony export */   USER_SIGNUP: () => (/* binding */ USER_SIGNUP),\n/* harmony export */   USER_SOCIAL_LOGIN: () => (/* binding */ USER_SOCIAL_LOGIN)\n/* harmony export */ });\n// const isProd = true;\n// process.env.NODE_ENV === \"production\";\nconst isProd = \"false\";\nconst devUrl = \"http://localhost:8000\";\n// const prodUrl = \"https://api-e-com.TRIUMPH ENTERPRISES.in\";\nconst prodUrl = \"http://localhost:8000\";\nconsole.log(\"process.env.IS_PROD\", \"false\");\nconsole.log(\"process.env.API_BACKEND_URL\", \"http://localhost:8000\");\nconst MAIN_URL = isProd ? prodUrl : devUrl;\nconsole.log(\"MAIN_URL\", MAIN_URL);\nconst version = \"/api/v1/\";\nconst PRODUCTS = `${version}products/`;\nconst CATEGORIES = `${version}products/categories/`;\nconst BRANDS = `${version}products/brands/`;\nconst USER_SIGNUP = `${version}users/`;\nconst USER_LOGIN = `${version}users/login/`;\nconst USER_LOGOUT = `${version}users/logout/`;\nconst USER_SOCIAL_LOGIN = `${version}users/social/login/`;\nconst USER_CART = `${version}orders/cart/`;\nconst ADD_TO_CART = `${version}orders/cart/add-item/`;\nconst UPDATE_CART = `${version}orders/cart/update-item/`;\nconst TOKEN_REFFRESH = `${version}users/token/refresh/`;\nconst USER_DETAIL = `${version}users/detail/`;\nconst USER_REFFRESH_BLACKLIST = `${version}users/token/blacklist/`;\nconst USER_ADDRESS = `${version}users/addresses/`;\nconst ORDERS = `${version}orders/`;\nconst ADD_TO_WISHLIST = `${version}users/wishlist/`;\nconst FUTURED_PRODUCTS = `${version}products/feature/products/`;\nconst RANDOM_PRODUCTS = `${version}products/feature/products/?random=true`;\nconst REMOVE_FROM_WISHLIST = `${version}users/remove/wishlist/`;\nconst CATEGORIZE_PRODUCTS = (slug)=>{\n    return `${version}products/categories/${slug}/products/`;\n};\nconst SHIPPING_METHODS = `${version}orders/shipping-methods/`;\nconst PROMOCODE_APPLY = `${version}promotions/apply/code/`;\nconst PROFILE_UPDATE = `${version}users/profile/update/`;\nconst GET_PROMO_CODE = `${version}promotions/get/single/promotion/`;\n// Payment URLs\nconst PAYMENTS_PHONEPE_INITIATE = `${version}payments/phonepe/initiate`;\n// Contact Form URL\nconst CONTACT_FORM = `${version}users/contact/`;\n// Password Reset URLs\nconst FORGOT_PASSWORD = `${version}users/forgot-password/`;\nconst RESET_PASSWORD = `${version}users/reset-password/`;\n// Rapidshyp Shipping Integration URLs\nconst SHIPPING_CALCULATE_RATES = `${version}shipping/calculate-rates/`;\nconst SHIPPING_VALIDATE_PINCODE = `${version}shipping/validate-pincode/`;\nconst SHIPPING_TRACK_ORDER = (orderId)=>`${version}shipping/track/${orderId}/`;\nconst SHIPPING_BULK_TRACK = `${version}shipping/bulk-track/`;\nconst SHIPPING_STATUS = `${version}shipping/status/`;\nconst SHIPPING_HEALTH = `${version}shipping/health/`;\n// Public Tracking URLs\nconst PUBLIC_ORDER_TRACKING = (orderId)=>`${version}orders/${orderId}/public-tracking/`;\nconst SEARCH_BY_TRACKING = (trackingNumber)=>`${version}orders/search-by-tracking/${trackingNumber}/`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./constant/urls.ts\n");

/***/ }),

/***/ "(rsc)/./lib/authOptions.ts":
/*!****************************!*\
  !*** ./lib/authOptions.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constant/urls */ \"(rsc)/./constant/urls.ts\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _secureStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./secureStorage */ \"(rsc)/./lib/secureStorage.ts\");\n\n\n\n\n\nconst authOptions = {\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            clientId: \"407412310348-82l3qbdon56udh9650abktvatl082hun.apps.googleusercontent.com\" || 0 || 0,\n            clientSecret: \"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\" || 0 || 0\n        }),\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            // Configure to use the custom API response\n            name: \"Credentials\",\n            credentials: {},\n            async authorize (credentials) {\n                try {\n                    const data = {\n                        ...credentials\n                    };\n                    if (data) {\n                        const user = {\n                            id: data.id || \"\",\n                            email: data.email || \"\",\n                            name: data.name || \"\",\n                            accessToken: data.accessToken || \"\",\n                            refreshToken: data.refreshToken || \"\",\n                            access: data.accessToken || \"\",\n                            refresh: data.refreshToken || \"\",\n                            phone: data.phone_number,\n                            dob: data.date_of_birth\n                        };\n                        return user;\n                    }\n                } catch (error) {\n                    console.error(\"Error in authorize:\", error);\n                    return null;\n                }\n                return null;\n            }\n        })\n    ],\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            if (account?.provider === \"google\") {\n                try {\n                    // Show loading state in the console\n                    console.log(\"Google sign-in in progress...\");\n                    console.log(\"Google profile data:\", profile);\n                    // Send Google user data to your backend API\n                    const res = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(`${_constant_urls__WEBPACK_IMPORTED_MODULE_2__.MAIN_URL}${_constant_urls__WEBPACK_IMPORTED_MODULE_2__.USER_SOCIAL_LOGIN}`, {\n                        email: user.email,\n                        name: user.name,\n                        image_url: user.image || profile?.picture,\n                        provider: \"google\"\n                    }, {\n                        headers: {\n                            \"Content-Type\": \"application/json\"\n                        }\n                    });\n                    if (res.status === 200) {\n                        const data = res.data;\n                        console.log(\"Social login success response:\", data);\n                        // Attach custom tokens to user object\n                        user.accessToken = data.accessToken; // Changed to match JWT callback\n                        user.refreshToken = data.refreshToken; // Changed to match JWT callback\n                        user.access = data.accessToken; // Keep for backward compatibility\n                        user.refresh = data.refreshToken; // Keep for backward compatibility\n                        user.phone = data.phone_number;\n                        user.dob = data.date_of_birth;\n                        user.id = Number(data.id);\n                        user.email = data.email;\n                        user.name = data.name;\n                        console.log(\"Google sign-in successful, redirecting...\");\n                        return true;\n                    } else {\n                        console.error(\"Backend login failed with status:\", res.status);\n                        return false;\n                    }\n                } catch (error) {\n                    console.error(\"Error during Google sign-in:\", error);\n                    if (axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].isAxiosError(error)) {\n                        console.error(\"Axios error details:\", error.response?.data);\n                    }\n                    return false;\n                }\n            }\n            return true;\n        },\n        async jwt ({ token, user }) {\n            if (user) {\n                console.log(\"=== JWT CALLBACK DEBUG ===\");\n                console.log(\"User object:\", user);\n                console.log(\"user.accessToken:\", user.accessToken);\n                console.log(\"user.access:\", user.access);\n                console.log(\"user.refreshToken:\", user.refreshToken);\n                console.log(\"user.refresh:\", user.refresh);\n                // Store tokens securely using our secure storage\n                if (user.accessToken && user.refreshToken) {\n                    _secureStorage__WEBPACK_IMPORTED_MODULE_3__.authUtils.storeAuthResponse({\n                        accessToken: user.accessToken,\n                        refreshToken: user.refreshToken,\n                        ...user\n                    });\n                }\n                token.access = user.accessToken || user.access;\n                token.refresh = user.refreshToken || user.refresh;\n                token.phone = user.phone;\n                token.dob = user.dob;\n                token.id = user.id;\n                token.email = user.email;\n                token.name = user.name;\n                token.accessTokenExpires = Date.now() + 15 * 60 * 1000; // 15 minutes as per backend config\n                console.log(\"Final token.access:\", token.access);\n                console.log(\"Final token.refresh:\", token.refresh);\n                console.log(\"=== END JWT CALLBACK DEBUG ===\");\n            }\n            // Check if token is still valid\n            if (Date.now() < token.accessTokenExpires) {\n                return token;\n            }\n            // Check if we have a refresh token before attempting refresh\n            if (!token.refresh) {\n                console.log(\"No refresh token available, forcing re-login\");\n                return {\n                    ...token,\n                    access: null,\n                    refresh: null,\n                    error: \"NoRefreshToken\"\n                };\n            }\n            // Access token expired, try to update it\n            return refreshAccessToken(token);\n        },\n        async session ({ session, token }) {\n            // If there's a token error, don't include access token in session\n            // This will force the frontend to redirect to login\n            if (token.error) {\n                console.log(\"Token error detected, session will not include access token:\", token.error);\n                session.user = {\n                    ...session.user,\n                    access: null,\n                    refresh: null,\n                    phone: token.phone,\n                    dob: token.dob,\n                    id: token.id,\n                    email: token.email ?? \"\",\n                    name: token.name ?? \"\",\n                    error: token.error\n                };\n            } else {\n                session.user = {\n                    ...session.user,\n                    access: token.access,\n                    refresh: token.refresh,\n                    phone: token.phone,\n                    dob: token.dob,\n                    id: token.id,\n                    email: token.email ?? \"\",\n                    name: token.name ?? \"\"\n                };\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: \"/auth/login\"\n    }\n};\nasync function refreshAccessToken(token) {\n    try {\n        console.log(\"=== TOKEN REFRESH DEBUG ===\");\n        console.log(\"Attempting to refresh token...\");\n        console.log(\"Refresh token exists:\", !!token.refresh);\n        console.log(\"Refresh token preview:\", token.refresh?.substring(0, 50) + \"...\");\n        console.log(\"Refresh URL:\", _constant_urls__WEBPACK_IMPORTED_MODULE_2__.MAIN_URL + _constant_urls__WEBPACK_IMPORTED_MODULE_2__.TOKEN_REFFRESH);\n        const res = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(_constant_urls__WEBPACK_IMPORTED_MODULE_2__.MAIN_URL + _constant_urls__WEBPACK_IMPORTED_MODULE_2__.TOKEN_REFFRESH, {\n            refresh: token.refresh\n        }, {\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        const refreshedTokens = res.data;\n        console.log(\"Token refresh successful!\");\n        console.log(\"New access token preview:\", refreshedTokens.access?.substring(0, 50) + \"...\");\n        return {\n            ...token,\n            access: refreshedTokens.access,\n            refresh: refreshedTokens.refresh,\n            accessTokenExpires: Date.now() + 15 * 60 * 1000\n        };\n    } catch (error) {\n        console.error(\"=== TOKEN REFRESH FAILED ===\");\n        console.error(\"Error refreshing access token:\", error);\n        if (axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].isAxiosError(error)) {\n            console.error(\"Response status:\", error.response?.status);\n            console.error(\"Response data:\", error.response?.data);\n            console.error(\"Request data:\", {\n                refresh: token.refresh\n            });\n        }\n        console.error(\"=== END TOKEN REFRESH DEBUG ===\");\n        // Instead of returning an error token, return the original token without access\n        // This will force a re-login\n        return {\n            ...token,\n            access: null,\n            refresh: null,\n            error: \"RefreshAccessTokenError\"\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/authOptions.ts\n");

/***/ }),

/***/ "(rsc)/./lib/secureStorage.ts":
/*!******************************!*\
  !*** ./lib/secureStorage.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SecureStorage: () => (/* binding */ SecureStorage),\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   authUtils: () => (/* binding */ authUtils),\n/* harmony export */   secureLocalStorage: () => (/* binding */ secureLocalStorage),\n/* harmony export */   secureSessionStorage: () => (/* binding */ secureSessionStorage),\n/* harmony export */   tokenManager: () => (/* binding */ tokenManager)\n/* harmony export */ });\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto-js */ \"(rsc)/./node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_0__);\n/**\r\n * Secure storage utilities for handling sensitive data in the browser\r\n * Provides encryption for localStorage/sessionStorage data\r\n */ \n// Use a stable encryption key to prevent data corruption\nconst getEncryptionKey = ()=>{\n    // Use a stable key that doesn't change based on browser characteristics\n    const baseKey = \"84ade154d8a5a91d1dfafe764aee3033c186bc48e4e3bdce6057c2b8d250ee3a\" || 0;\n    // For additional security, you could use a stable device identifier\n    // stored in localStorage, but avoid dynamic browser fingerprinting\n    let deviceId = '';\n    if (false) {}\n    return crypto_js__WEBPACK_IMPORTED_MODULE_0___default().SHA256(baseKey + deviceId).toString();\n};\nclass SecureStorage {\n    constructor(options = {}){\n        this.encryptionKey = getEncryptionKey();\n        if (false) {} else {\n            this.storage = null;\n        }\n    }\n    /**\r\n   * Encrypt and store data\r\n   */ setItem(key, value, expirationMinutes) {\n        if (!this.storage) return false;\n        try {\n            const dataToStore = {\n                value,\n                timestamp: Date.now(),\n                expiration: expirationMinutes ? Date.now() + expirationMinutes * 60 * 1000 : null\n            };\n            const encrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.encrypt(JSON.stringify(dataToStore), this.encryptionKey).toString();\n            this.storage.setItem(key, encrypted);\n            return true;\n        } catch (error) {\n            console.error('SecureStorage: Failed to store item', error);\n            return false;\n        }\n    }\n    /**\r\n   * Retrieve and decrypt data\r\n   */ getItem(key) {\n        if (!this.storage) return null;\n        try {\n            const encrypted = this.storage.getItem(key);\n            if (!encrypted) return null;\n            // Check if the data is already in plain text (for backward compatibility)\n            try {\n                const plainData = JSON.parse(encrypted);\n                if (plainData && typeof plainData === 'object' && plainData.value !== undefined) {\n                    // This looks like unencrypted data, migrate it\n                    this.setItem(key, plainData.value, plainData.expiration ? Math.max(0, Math.floor((plainData.expiration - Date.now()) / (60 * 1000))) : undefined);\n                    return plainData.value;\n                }\n            } catch  {\n            // Not plain JSON, continue with decryption\n            }\n            const decrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encrypted, this.encryptionKey);\n            const decryptedString = decrypted.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n            if (!decryptedString) {\n                // Decryption failed, likely due to key mismatch\n                console.warn(`SecureStorage: Failed to decrypt item '${key}', removing corrupted data`);\n                this.removeItem(key);\n                return null;\n            }\n            // Validate that the decrypted string is valid JSON\n            let data;\n            try {\n                data = JSON.parse(decryptedString);\n            } catch (parseError) {\n                // Malformed JSON after decryption indicates key mismatch\n                console.warn(`SecureStorage: Malformed data for item '${key}', removing corrupted data`);\n                this.removeItem(key);\n                return null;\n            }\n            // Check expiration\n            if (data.expiration && Date.now() > data.expiration) {\n                this.removeItem(key);\n                return null;\n            }\n            return data.value;\n        } catch (error) {\n            console.error(`SecureStorage: Failed to retrieve item '${key}'`, error);\n            // Remove corrupted item to prevent future errors\n            try {\n                this.removeItem(key);\n            } catch (removeError) {\n                console.error(`SecureStorage: Failed to remove corrupted item '${key}'`, removeError);\n            }\n            return null;\n        }\n    }\n    /**\r\n   * Remove item from storage\r\n   */ removeItem(key) {\n        if (!this.storage) return;\n        this.storage.removeItem(key);\n    }\n    /**\r\n   * Clear all items from storage\r\n   */ clear() {\n        if (!this.storage) return;\n        this.storage.clear();\n    }\n    /**\r\n   * Check if item exists and is not expired\r\n   */ hasItem(key) {\n        return this.getItem(key) !== null;\n    }\n    /**\r\n   * Get all keys in storage\r\n   */ getAllKeys() {\n        if (!this.storage) return [];\n        const keys = [];\n        for(let i = 0; i < this.storage.length; i++){\n            const key = this.storage.key(i);\n            if (key) keys.push(key);\n        }\n        return keys;\n    }\n    /**\r\n   * Migrate corrupted data by clearing items that cannot be decrypted\r\n   */ migrateCorruptedData() {\n        if (!this.storage) return 0;\n        const keys = this.getAllKeys();\n        let migratedCount = 0;\n        keys.forEach((key)=>{\n            try {\n                const encrypted = this.storage.getItem(key);\n                if (!encrypted) return;\n                // Try to decrypt without throwing errors\n                const decrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encrypted, this.encryptionKey);\n                const decryptedString = decrypted.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n                if (!decryptedString) {\n                    // Decryption failed, remove item\n                    this.removeItem(key);\n                    migratedCount++;\n                    return;\n                }\n                // Try to parse JSON\n                try {\n                    JSON.parse(decryptedString);\n                } catch (parseError) {\n                    // Invalid JSON, remove item\n                    this.removeItem(key);\n                    migratedCount++;\n                }\n            } catch (error) {\n                // Any other error, remove item\n                this.removeItem(key);\n                migratedCount++;\n            }\n        });\n        if (migratedCount > 0) {\n            console.log(`SecureStorage: Migrated ${migratedCount} corrupted items`);\n        }\n        return migratedCount;\n    }\n    /**\r\n   * Clear all encrypted data (use when encryption key changes)\r\n   */ clearAllEncryptedData() {\n        if (!this.storage) return;\n        const keys = this.getAllKeys();\n        let clearedCount = 0;\n        keys.forEach((key)=>{\n            // Skip device_id and other non-encrypted items\n            if (key === 'device_id') return;\n            this.removeItem(key);\n            clearedCount++;\n        });\n        console.log(`SecureStorage: Cleared ${clearedCount} encrypted items`);\n    }\n    /**\r\n   * Clean up expired items\r\n   */ cleanupExpired() {\n        if (!this.storage) return 0;\n        const keys = this.getAllKeys();\n        let cleanedCount = 0;\n        keys.forEach((key)=>{\n            try {\n                const encrypted = this.storage.getItem(key);\n                if (!encrypted) return;\n                const decrypted = crypto_js__WEBPACK_IMPORTED_MODULE_0___default().AES.decrypt(encrypted, this.encryptionKey);\n                const decryptedString = decrypted.toString((crypto_js__WEBPACK_IMPORTED_MODULE_0___default().enc).Utf8);\n                if (!decryptedString) {\n                    this.removeItem(key);\n                    cleanedCount++;\n                    return;\n                }\n                const data = JSON.parse(decryptedString);\n                if (data.expiration && Date.now() > data.expiration) {\n                    this.removeItem(key);\n                    cleanedCount++;\n                }\n            } catch (error) {\n                // Remove corrupted items\n                this.removeItem(key);\n                cleanedCount++;\n            }\n        });\n        return cleanedCount;\n    }\n}\n// Pre-configured instances\nconst secureLocalStorage = new SecureStorage({\n    useSessionStorage: false\n});\nconst secureSessionStorage = new SecureStorage({\n    useSessionStorage: true\n});\n// Token management utilities\nclass TokenManager {\n    constructor(useSessionStorage = true){\n        this.storage = new SecureStorage({\n            useSessionStorage\n        });\n    }\n    /**\r\n   * Store authentication tokens\r\n   */ setTokens(accessToken, refreshToken) {\n        // Access token expires in 15 minutes (as per backend config)\n        this.storage.setItem('access_token', accessToken, 15);\n        // Refresh token expires in 7 days (as per backend config)\n        this.storage.setItem('refresh_token', refreshToken, 7 * 24 * 60);\n    }\n    /**\r\n   * Get access token\r\n   */ getAccessToken() {\n        return this.storage.getItem('access_token');\n    }\n    /**\r\n   * Get refresh token\r\n   */ getRefreshToken() {\n        return this.storage.getItem('refresh_token');\n    }\n    /**\r\n   * Remove all tokens\r\n   */ clearTokens() {\n        this.storage.removeItem('access_token');\n        this.storage.removeItem('refresh_token');\n    }\n    /**\r\n   * Check if user is authenticated\r\n   */ isAuthenticated() {\n        return this.getAccessToken() !== null;\n    }\n    /**\r\n   * Check if refresh token is available\r\n   */ canRefresh() {\n        return this.getRefreshToken() !== null;\n    }\n    /**\r\n   * Store user data\r\n   */ setUserData(userData) {\n        this.storage.setItem('user_data', userData, 24 * 60); // 24 hours\n    }\n    /**\r\n   * Get user data\r\n   */ getUserData() {\n        return this.storage.getItem('user_data');\n    }\n    /**\r\n   * Clear user data\r\n   */ clearUserData() {\n        this.storage.removeItem('user_data');\n    }\n    /**\r\n   * Clear all authentication data\r\n   */ logout() {\n        this.clearTokens();\n        this.clearUserData();\n        // Clean up any other auth-related data\n        this.storage.removeItem('cart_data');\n        this.storage.removeItem('wishlist_data');\n    }\n}\n// Default token manager instance\nconst tokenManager = new TokenManager(true); // Use session storage\n// Utility functions for common operations\nconst authUtils = {\n    /**\r\n   * Store authentication response\r\n   */ storeAuthResponse: (response)=>{\n        tokenManager.setTokens(response.accessToken, response.refreshToken);\n        // Store user data (excluding tokens)\n        const { accessToken, refreshToken, ...userData } = response;\n        tokenManager.setUserData(userData);\n    },\n    /**\r\n   * Get authorization header\r\n   */ getAuthHeader: ()=>{\n        const token = tokenManager.getAccessToken();\n        return token ? {\n            Authorization: `Bearer ${token}`\n        } : {};\n    },\n    /**\r\n   * Check if user is authenticated\r\n   */ isAuthenticated: ()=>{\n        return tokenManager.isAuthenticated();\n    },\n    /**\r\n   * Logout user\r\n   */ logout: ()=>{\n        tokenManager.logout();\n    },\n    /**\r\n   * Get current user data\r\n   */ getCurrentUser: ()=>{\n        return tokenManager.getUserData();\n    }\n};\n// Auto cleanup expired items on page load\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/secureStorage.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var D_Triumph_ecommerce_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./app/api/auth/[...nextauth]/route.ts */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/[...nextauth]/route\",\n        pathname: \"/api/auth/[...nextauth]\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/[...nextauth]/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\api\\\\auth\\\\[...nextauth]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Triumph_ecommerce_app_api_auth_nextauth_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/auth/[...nextauth]/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/crypto-js","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/next-auth","vendor-chunks/follow-redirects","vendor-chunks/@babel","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/jose","vendor-chunks/openid-client","vendor-chunks/uuid","vendor-chunks/oauth","vendor-chunks/@panva","vendor-chunks/yallist","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/preact","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&page=%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2F%5B...nextauth%5D%2Froute.ts&appDir=D%3A%5CTriumph%5Cecommerce%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CTriumph%5Cecommerce&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();