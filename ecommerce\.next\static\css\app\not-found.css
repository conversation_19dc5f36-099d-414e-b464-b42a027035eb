/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./styles/not-found.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/* 404 Page Animations and Styles */

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.not-found-container {
  animation: fadeInUp 0.8s ease-out;
}

.not-found-404-text {
  background: linear-gradient(
    90deg,
    #2ECC71 0%,
    #27AE60 25%,
    #2ECC71 50%,
    #27AE60 75%,
    #2ECC71 100%
  );
  background-size: 200px 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 3s ease-in-out infinite;
}

.not-found-icon {
  animation: float 3s ease-in-out infinite;
}

.not-found-card {
  animation: bounceIn 1s ease-out 0.3s both;
}

.not-found-popular-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.not-found-popular-item:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .not-found-404-text {
    font-size: 6rem;
  }
}

@media (max-width: 480px) {
  .not-found-404-text {
    font-size: 4rem;
  }
}

/* Loading state for better UX */
.not-found-loading {
  opacity: 0;
  animation: fadeInUp 0.6s ease-out 0.1s forwards;
}

/* Hover effects for buttons */
.not-found-button {
  position: relative;
  overflow: hidden;
}

.not-found-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.not-found-button:hover::before {
  left: 100%;
}

