{"name": "Triumph Enterprises", "slug": "Triumph Enterprises", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/triumph logo.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"useNextNotificationsApi": true, "adaptiveIcon": {"foregroundImage": "./assets/triumph logo.png", "backgroundColor": "#ffffff"}, "package": "com.sohangpurh53.ecommtest"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/triumph logo.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/triumph logo.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-secure-store", {"configureAndroidBackup": true, "faceIDPermission": "Allow $(PRODUCT_NAME) to access your Face ID biometric data."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "f2a88e2c-0b3b-4a12-9776-ebc3b5b4bde5"}}, "sdkVersion": "52.0.0", "platforms": ["ios", "android", "web"], "androidStatusBar": {"backgroundColor": "#ffffff"}}