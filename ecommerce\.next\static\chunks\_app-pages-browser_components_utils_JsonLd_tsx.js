"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_utils_JsonLd_tsx"],{

/***/ "(app-pages-browser)/./components/utils/JsonLd.tsx":
/*!*************************************!*\
  !*** ./components/utils/JsonLd.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ JsonLd)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/script */ \"(app-pages-browser)/./node_modules/next/dist/api/script.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction JsonLd(param) {\n    let { organizationName = 'Triumph Enterprises', url, logo, siteTitle = 'Triumph Enterprises | Premium Hardware & Security Solutions', siteDescription = 'Triumph Enterprises offers high-quality hardware, security products, and home solutions. Shop our wide range of door locks, digital safes, and more.' } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Dynamically determine the base URL based on the current environment\n    const baseUrl = url || ( true ? window.location.origin : 0);\n    const logoUrl = logo || \"\".concat(baseUrl, \"/logotriumph.png\");\n    const currentUrl = \"\".concat(baseUrl).concat(pathname);\n    const organizationSchema = {\n        '@context': 'https://schema.org',\n        '@type': 'Organization',\n        name: organizationName,\n        url: baseUrl,\n        logo: logoUrl,\n        sameAs: [\n            'https://facebook.com/triumphenterprises',\n            'https://twitter.com/triumphenterprises',\n            'https://instagram.com/triumphenterprises'\n        ],\n        contactPoint: {\n            '@type': 'ContactPoint',\n            telephone: '+91-1234567890',\n            contactType: 'customer service',\n            availableLanguage: [\n                'English',\n                'Hindi'\n            ]\n        }\n    };\n    const websiteSchema = {\n        '@context': 'https://schema.org',\n        '@type': 'WebSite',\n        name: siteTitle,\n        url: baseUrl,\n        description: siteDescription,\n        potentialAction: {\n            '@type': 'SearchAction',\n            target: \"\".concat(baseUrl, \"/shop?search={search_term_string}\"),\n            'query-input': 'required name=search_term_string'\n        }\n    };\n    const breadcrumbSchema = {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: [\n            {\n                '@type': 'ListItem',\n                position: 1,\n                name: 'Home',\n                item: baseUrl\n            },\n            ...pathname !== '/' ? [\n                {\n                    '@type': 'ListItem',\n                    position: 2,\n                    name: pathname.split('/')[1].charAt(0).toUpperCase() + pathname.split('/')[1].slice(1),\n                    item: currentUrl\n                }\n            ] : []\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: \"organization-schema\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(organizationSchema)\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\JsonLd.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: \"website-schema\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(websiteSchema)\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\JsonLd.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                id: \"breadcrumb-schema\",\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(breadcrumbSchema)\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\utils\\\\JsonLd.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(JsonLd, \"xbyQPtUVMO7MNj7WjJlpdWqRcTo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\n_c = JsonLd;\nvar _c;\n$RefreshReg$(_c, \"JsonLd\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/utils/JsonLd.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXFRyaXVtcGhcXGVjb21tZXJjZVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/script.js":
/*!**********************************************!*\
  !*** ./node_modules/next/dist/api/script.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport default from dynamic */ _client_script__WEBPACK_IMPORTED_MODULE_0___default.a)\n/* harmony export */ });\n/* harmony import */ var _client_script__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/script */ \"(app-pages-browser)/./node_modules/next/dist/client/script.js\");\n/* harmony import */ var _client_script__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_script__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_script__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_script__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=script.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL3NjcmlwdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDVjs7QUFFakMiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxzY3JpcHQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgZGVmYXVsdCB9IGZyb20gJy4uL2NsaWVudC9zY3JpcHQnO1xuZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L3NjcmlwdCc7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNjcmlwdC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/script.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    cancelIdleCallback: function() {\n        return cancelIdleCallback;\n    },\n    requestIdleCallback: function() {\n        return requestIdleCallback;\n    }\n});\nconst requestIdleCallback = typeof self !== 'undefined' && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nconst cancelIdleCallback = typeof self !== 'undefined' && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    handleClientScriptLoad: function() {\n        return handleClientScriptLoad;\n    },\n    initScriptLoader: function() {\n        return initScriptLoader;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _setattributesfromprops = __webpack_require__(/*! ./set-attributes-from-props */ \"(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst insertStylesheets = (stylesheets)=>{\n    // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n    //\n    // Using ReactDOM.preinit to feature detect appDir and inject styles\n    // Stylesheets might have already been loaded if initialized with Script component\n    // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n    // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n    if (_reactdom.default.preinit) {\n        stylesheets.forEach((stylesheet)=>{\n            _reactdom.default.preinit(stylesheet, {\n                as: 'style'\n            });\n        });\n        return;\n    }\n    // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n    //\n    // We use this function to load styles when appdir is not detected\n    // TODO: Use React float APIs to load styles once available for pages dir\n    if (true) {\n        let head = document.head;\n        stylesheets.forEach((stylesheet)=>{\n            let link = document.createElement('link');\n            link.type = 'text/css';\n            link.rel = 'stylesheet';\n            link.href = stylesheet;\n            head.appendChild(link);\n        });\n    }\n};\nconst loadScript = (props)=>{\n    const { src, id, onLoad = ()=>{}, onReady = null, dangerouslySetInnerHTML, children = '', strategy = 'afterInteractive', onError, stylesheets } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n        // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    /** Execute after the script first loaded */ const afterLoad = ()=>{\n        // Run onReady for the first time after load event\n        if (onReady) {\n            onReady();\n        }\n        // add cacheKey to LoadCache when load successfully\n        LoadCache.add(cacheKey);\n    };\n    const el = document.createElement('script');\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener('load', function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n            afterLoad();\n        });\n        el.addEventListener('error', function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (dangerouslySetInnerHTML) {\n        // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n        afterLoad();\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n        afterLoad();\n    } else if (src) {\n        el.src = src;\n        // do not add cacheKey into LoadCache for remote script here\n        // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n        ScriptCache.set(src, loadPromise);\n    }\n    (0, _setattributesfromprops.setAttributesFromProps)(el, props);\n    if (strategy === 'worker') {\n        el.setAttribute('type', 'text/partytown');\n    }\n    el.setAttribute('data-nscript', strategy);\n    // Load styles associated with this script\n    if (stylesheets) {\n        insertStylesheets(stylesheets);\n    }\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy = 'afterInteractive' } = props;\n    if (strategy === 'lazyOnload') {\n        window.addEventListener('load', ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    } else {\n        loadScript(props);\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === 'complete') {\n        (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n    } else {\n        window.addEventListener('load', ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    }\n}\nfunction addBeforeInteractiveToCache() {\n    const scripts = [\n        ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n        ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]')\n    ];\n    scripts.forEach((script)=>{\n        const cacheKey = script.id || script.getAttribute('src');\n        LoadCache.add(cacheKey);\n    });\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n    addBeforeInteractiveToCache();\n}\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */ function Script(props) {\n    const { id, src = '', onLoad = ()=>{}, onReady = null, strategy = 'afterInteractive', onError, stylesheets, ...restProps } = props;\n    // Context is available only during SSR\n    let { updateScripts, scripts, getIsSsr, appDir, nonce } = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    // if a nonce is explicitly passed to the script tag, favor that over the automatic handling\n    nonce = restProps.nonce || nonce;\n    /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */ const hasOnReadyEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        const cacheKey = id || src;\n        if (!hasOnReadyEffectCalled.current) {\n            // Run onReady if script has loaded before but component is re-mounted\n            if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n                onReady();\n            }\n            hasOnReadyEffectCalled.current = true;\n        }\n    }, [\n        onReady,\n        id,\n        src\n    ]);\n    const hasLoadScriptEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        if (!hasLoadScriptEffectCalled.current) {\n            if (strategy === 'afterInteractive') {\n                loadScript(props);\n            } else if (strategy === 'lazyOnload') {\n                loadLazyScript(props);\n            }\n            hasLoadScriptEffectCalled.current = true;\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === 'beforeInteractive' || strategy === 'worker') {\n        if (updateScripts) {\n            scripts[strategy] = (scripts[strategy] || []).concat([\n                {\n                    id,\n                    src,\n                    onLoad,\n                    onReady,\n                    onError,\n                    ...restProps,\n                    nonce\n                }\n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript({\n                ...props,\n                nonce\n            });\n        }\n    }\n    // For the app directory, we need React Float to preload these scripts.\n    if (appDir) {\n        // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n        // For other strategies injecting here ensures correct stylesheet order\n        // ReactDOM.preinit handles loading the styles in the correct order,\n        // also ensures the stylesheet is loaded only once and in a consistent manner\n        //\n        // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n        // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n        // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n        // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n        if (stylesheets) {\n            stylesheets.forEach((styleSrc)=>{\n                _reactdom.default.preinit(styleSrc, {\n                    as: 'style'\n                });\n            });\n        }\n        // Before interactive scripts need to be loaded by Next.js' runtime instead\n        // of native <script> tags, because they no longer have `defer`.\n        if (strategy === 'beforeInteractive') {\n            if (!src) {\n                // For inlined scripts, we put the content in `children`.\n                if (restProps.dangerouslySetInnerHTML) {\n                    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n                    restProps.children = restProps.dangerouslySetInnerHTML.__html;\n                    delete restProps.dangerouslySetInnerHTML;\n                }\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            0,\n                            {\n                                ...restProps,\n                                id\n                            }\n                        ]) + \")\"\n                    }\n                });\n            } else {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: 'script',\n                    integrity: restProps.integrity,\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                } : {\n                    as: 'script',\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                });\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            src,\n                            {\n                                ...restProps,\n                                id\n                            }\n                        ]) + \")\"\n                    }\n                });\n            }\n        } else if (strategy === 'afterInteractive') {\n            if (src) {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: 'script',\n                    integrity: restProps.integrity,\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                } : {\n                    as: 'script',\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                });\n            }\n        }\n    }\n    return null;\n}\n_c = Script;\nObject.defineProperty(Script, '__nextScript', {\n    value: true\n});\nconst _default = Script;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=script.js.map\nvar _c;\n$RefreshReg$(_c, \"Script\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/script.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/set-attributes-from-props.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"setAttributesFromProps\", ({\n    enumerable: true,\n    get: function() {\n        return setAttributesFromProps;\n    }\n}));\nconst DOMAttributeNames = {\n    acceptCharset: 'accept-charset',\n    className: 'class',\n    htmlFor: 'for',\n    httpEquiv: 'http-equiv',\n    noModule: 'noModule'\n};\nconst ignoreProps = [\n    'onLoad',\n    'onReady',\n    'dangerouslySetInnerHTML',\n    'children',\n    'onError',\n    'strategy',\n    'stylesheets'\n];\nfunction isBooleanScriptAttribute(attr) {\n    return [\n        'async',\n        'defer',\n        'noModule'\n    ].includes(attr);\n}\nfunction setAttributesFromProps(el, props) {\n    for (const [p, value] of Object.entries(props)){\n        if (!props.hasOwnProperty(p)) continue;\n        if (ignoreProps.includes(p)) continue;\n        // we don't render undefined props to the DOM\n        if (value === undefined) {\n            continue;\n        }\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n            // Correctly assign boolean script attributes\n            // https://github.com/vercel/next.js/pull/20748\n            ;\n            el[attr] = !!value;\n        } else {\n            el.setAttribute(attr, String(value));\n        }\n        // Remove falsy non-zero boolean attributes so they are correctly interpreted\n        // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n        if (value === false || el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr) && (!value || value === 'false')) {\n            // Call setAttribute before, as we need to set and unset the attribute to override force async:\n            // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n            el.setAttribute(attr, '');\n            el.removeAttribute(attr);\n        }\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=set-attributes-from-props.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js\n"));

/***/ })

}]);