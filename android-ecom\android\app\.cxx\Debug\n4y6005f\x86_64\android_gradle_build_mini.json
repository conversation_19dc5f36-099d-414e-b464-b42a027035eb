{"buildFiles": ["D:\\Triumph\\android-ecom\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Triumph\\android-ecom\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Triumph\\android-ecom\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Triumph\\android-ecom\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Triumph\\android-ecom\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Triumph\\android-ecom\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Triumph\\android-ecom\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Triumph\\android-ecom\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Triumph\\android-ecom\\android\\app\\.cxx\\Debug\\n4y6005f\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Triumph\\android-ecom\\android\\app\\.cxx\\Debug\\n4y6005f\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "x86_64", "output": "D:\\Triumph\\android-ecom\\android\\app\\build\\intermediates\\cxx\\Debug\\n4y6005f\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6118f662953cfd7a4a508c73e43e201c\\transformed\\jetified-fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"artifactName": "react_codegen_RNCWebViewSpec", "abi": "x86_64", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "x86_64", "output": "D:\\Triumph\\android-ecom\\android\\app\\build\\intermediates\\cxx\\Debug\\n4y6005f\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6118f662953cfd7a4a508c73e43e201c\\transformed\\jetified-fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2": {"artifactName": "react_codegen_RNDateTimePickerCGen", "abi": "x86_64", "runtimeFiles": []}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "x86_64", "output": "D:\\Triumph\\android-ecom\\android\\app\\build\\intermediates\\cxx\\Debug\\n4y6005f\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["D:\\Triumph\\android-ecom\\android\\app\\build\\intermediates\\cxx\\Debug\\n4y6005f\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "D:\\Triumph\\android-ecom\\android\\app\\build\\intermediates\\cxx\\Debug\\n4y6005f\\obj\\x86_64\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6118f662953cfd7a4a508c73e43e201c\\transformed\\jetified-fbjni-0.6.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "x86_64", "runtimeFiles": []}}}