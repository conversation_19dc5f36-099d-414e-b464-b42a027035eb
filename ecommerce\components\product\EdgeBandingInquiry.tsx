"use client";

import React, { useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { useToast } from '../ui/use-toast';
import { ProductDetail } from '../../types/product';
import { MAIN_URL } from '../../constant/urls';
import axios from 'axios';
import { Mail, Phone, Building, Package, Ruler, Layers, MessageSquare, Palette } from 'lucide-react';
import useApi from '@/hooks/useApi';



interface EdgeBandingInquiryProps {
  product: ProductDetail;
  onClose?: () => void;
}

interface InquiryFormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  color: string;
  size: string;
  thickness: string;
  finish: string;
  quantity: number;
  message: string;
}

const EdgeBandingInquiry: React.FC<EdgeBandingInquiryProps> = ({ product, onClose }) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {create} = useApi(MAIN_URL);
  const [formData, setFormData] = useState<InquiryFormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    color: '',
    size: '',
    thickness: '',
    finish: '',
    quantity: 1,
    message: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'quantity' ? parseInt(value) || 1 : value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const inquiryData = {
        product: product.id,
        ...formData
      };

      const response = await create(`/api/v1/products/send/inquiries/`, inquiryData);
      if (response && response?.inquiry_id) {
        toast({
          title: "Inquiry Submitted Successfully!",
          description: "We'll get back to you within 24-48 hours with detailed information.",
          variant: "default"
        });

        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          company: '',
          color: '',
          size: '',
          thickness: '',
          finish: '',
          quantity: 1,
          message: ''
        });

        // Close modal if callback provided
        if (onClose) {
          setTimeout(() => onClose(), 2000);
        }
      }
    } catch (error: any) {
      console.error('Error submitting inquiry:', error);
      
      let errorMessage = "Failed to submit inquiry. Please try again.";
      if (error.response?.data) {
        const errorData = error.response.data;
        if (typeof errorData === 'object') {
          errorMessage = Object.values(errorData).flat().join(', ');
        } else if (typeof errorData === 'string') {
          errorMessage = errorData;
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Product Inquiry: {product.name}
        </h2>
        <p className="text-gray-600">
          Fill out the form below to get detailed information about this edge banding product including pricing and availability.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Customer Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              <Mail className="inline w-4 h-4 mr-1" />
              Full Name *
            </label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Your full name"
              required
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              <Mail className="inline w-4 h-4 mr-1" />
              Email Address *
            </label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
              <Phone className="inline w-4 h-4 mr-1" />
              Phone Number
            </label>
            <Input
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="+91 XXXXX XXXXX"
            />
          </div>

          <div>
            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
              <Building className="inline w-4 h-4 mr-1" />
              Company Name
            </label>
            <Input
              id="company"
              name="company"
              value={formData.company}
              onChange={handleInputChange}
              placeholder="Your company name"
            />
          </div>
        </div>

        {/* Product Specifications */}
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Specifications</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-1">
                <Package className="inline w-4 h-4 mr-1" />
                Color
              </label>
              <Input
                id="color"
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                placeholder="e.g., White, Oak, Walnut, etc."
              />
            </div>

            <div>
              <label htmlFor="finish" className="block text-sm font-medium text-gray-700 mb-1">
                <Palette className="inline w-4 h-4 mr-1" />
                Finish Type
              </label>
              {product.available_finishes && product.available_finishes.length > 0 ? (
                <Select onValueChange={(value) => handleSelectChange('finish', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select finish type" />
                  </SelectTrigger>
                  <SelectContent>
                    {product.available_finishes.map((finish) => (
                      <SelectItem key={finish} value={finish}>
                        {finish}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  id="finish"
                  name="finish"
                  value={formData.finish}
                  onChange={handleInputChange}
                  placeholder="e.g., Matte, Glossy, Semi-Gloss"
                />
              )}
            </div>

            <div>
              <label htmlFor="size" className="block text-sm font-medium text-gray-700 mb-1">
                <Ruler className="inline w-4 h-4 mr-1" />
                Size/Dimensions
              </label>
              <Input
                id="size"
                name="size"
                value={formData.size}
                onChange={handleInputChange}
                placeholder="e.g., 22mm x 50m, Custom size"
              />
            </div>

            <div>
              <label htmlFor="thickness" className="block text-sm font-medium text-gray-700 mb-1">
                <Layers className="inline w-4 h-4 mr-1" />
                Thickness
              </label>
              {product.available_thicknesses && product.available_thicknesses.length > 0 ? (
                <Select onValueChange={(value) => handleSelectChange('thickness', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select thickness" />
                  </SelectTrigger>
                  <SelectContent>
                    {product.available_thicknesses.map((thickness) => (
                      <SelectItem key={thickness} value={thickness}>
                        {thickness}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <Input
                  id="thickness"
                  name="thickness"
                  value={formData.thickness}
                  onChange={handleInputChange}
                  placeholder="e.g., 0.5mm, 1mm, 2mm"
                />
              )}
            </div>

            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                Quantity
              </label>
              <Input
                id="quantity"
                name="quantity"
                type="number"
                min="1"
                value={formData.quantity}
                onChange={handleInputChange}
                placeholder="1"
              />
            </div>
          </div>
        </div>

        {/* Additional Message */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
            <MessageSquare className="inline w-4 h-4 mr-1" />
            Additional Requirements/Message
          </label>
          <Textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            placeholder="Any specific requirements, questions, or additional information..."
            rows={4}
          />
        </div>

        {/* Submit Button */}
        <div className="flex gap-4">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? "Submitting..." : "Submit Inquiry"}
          </Button>
          
          {onClose && (
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}
        </div>
      </form>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-800">
          <strong>What happens next?</strong> Our team will review your inquiry and respond within 24-48 hours with detailed pricing, availability, and technical specifications.
        </p>
      </div>
    </div>
  );
};

export default EdgeBandingInquiry;
