# Edge Banding Product Implementation

This document describes the implementation of edge banding products with inquiry functionality, similar to IndiaMART's product inquiry system.

## Features Implemented

### 1. Backend (Django API)

#### Models
- **Product Model Extensions**:
  - `is_edge_banding`: Boolean field to mark products as edge banding
  - `available_thicknesses`: JSON field storing available thickness options (e.g., ['0.5mm', '1mm', '2mm'])
  - `available_finishes`: JSON field storing available finish types (e.g., ['Matte', 'Glossy', 'Semi-Gloss'])

- **ProductInquiry Model**: New model to handle product inquiries
  - Customer information (name, email, phone, company)
  - Product specifications (color, finish, size, thickness, quantity)
  - Status tracking (pending, contacted, quoted, completed, cancelled)
  - Admin notes for internal use

#### API Endpoints
- `POST /products/inquiries/` - Submit product inquiry
- `GET /products/admin/inquiries/` - List all inquiries (admin only)
- `GET /products/admin/inquiries/{id}/` - View/edit specific inquiry (admin only)

#### Email System
- Automatic confirmation email to customer
- Notification email to admin
- Email templates for both HTML and text formats

### 2. Frontend (Next.js)

#### Components
- **EdgeBandingInquiry**: Complete inquiry form component
- **ThicknessSelector**: Specialized component for thickness selection
- **FinishSelector**: Specialized component for finish type selection
- **ProductInfo**: Updated to show inquiry button for edge banding products

#### Features
- Modal dialog for inquiry form
- Thickness selection from available options
- Form validation and error handling
- Success notifications
- Responsive design

## File Structure

### Backend Files
```
e-com-2024-apis/
├── products/
│   ├── models.py                    # Added ProductInquiry model and edge banding fields
│   ├── serializers.py               # Added ProductInquirySerializer
│   ├── views.py                     # Added inquiry views
│   ├── urls.py                      # Added inquiry endpoints
│   ├── admin.py                     # Added ProductInquiry admin
│   └── migrations/
│       └── 0002_add_edge_banding_fields.py
├── users/
│   ├── utils.py                     # Added inquiry email functions
│   └── templates/emails/
│       ├── inquiry_confirmation.txt
│       ├── inquiry_confirmation.html
│       ├── inquiry_notification.txt
│       └── inquiry_notification.html
└── test_edge_banding.py             # Test script
```

### Frontend Files
```
ecommerce/
├── components/product/
│   ├── EdgeBandingInquiry.tsx       # Main inquiry form component
│   ├── ThicknessSelector.tsx        # Thickness selection component
│   └── ProductInfo.tsx              # Updated with inquiry functionality
└── types/
    └── product.d.ts                 # Updated with edge banding fields
```

## Usage Instructions

### 1. Database Setup
```bash
# Run migrations to create new tables and fields
python manage.py migrate
```

### 2. Create Edge Banding Products
1. Go to Django admin panel
2. Create or edit a product
3. Check "Is edge banding" checkbox
4. Add available thicknesses in JSON format: `["0.5mm", "1mm", "2mm"]`
5. Save the product

### 3. Frontend Usage
- Edge banding products will automatically show "Request Quote" button instead of "Add to Cart"
- Customers can fill out inquiry form with specifications
- Form includes thickness selector if options are available
- Automatic email notifications are sent

### 4. Admin Management
- View all inquiries in Django admin under "Product Inquiries"
- Filter by status, date, product category
- Update inquiry status and add admin notes
- Export inquiry data if needed

## API Usage Examples

### Submit Inquiry
```javascript
POST /products/inquiries/
{
  "product": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+91 9876543210",
  "company": "ABC Furniture",
  "color": "White Oak",
  "finish": "matte",
  "size": "22mm x 50m",
  "thickness": "1mm",
  "quantity": 10,
  "message": "Need bulk pricing for this specification"
}
```

### List Inquiries (Admin)
```javascript
GET /products/admin/inquiries/?status=pending&product_id=123
```

## Configuration

### Email Settings
Add these settings to your Django settings:
```python
# Email configuration for inquiries
ADMIN_EMAIL = '<EMAIL>'
COMPANY_NAME = 'Your Company Name'
COMPANY_PHONE = '+91-XXXXXXXXXX'
ADMIN_URL = 'https://yourdomain.com/admin'
```

### Frontend Environment
Ensure your frontend has the correct API URL:
```javascript
// constant/urls.ts
export const MAIN_URL = 'http://your-api-domain.com/api'
```

## Testing

Run the test script to verify implementation:
```bash
python test_edge_banding.py
```

## Customization

### Adding More Fields
To add more inquiry fields:
1. Update `ProductInquiry` model
2. Create migration
3. Update `ProductInquirySerializer`
4. Update `EdgeBandingInquiry` component
5. Update email templates

### Custom Email Templates
Edit templates in `users/templates/emails/` to customize email content and styling.

### Status Workflow
Modify `ProductInquiry.STATUS_CHOICES` to add custom status options for your workflow.

## Security Considerations

- All inquiry endpoints use proper authentication/authorization
- Email addresses are validated
- Input sanitization is handled by Django forms
- CSRF protection is enabled
- Rate limiting can be added if needed

## Future Enhancements

- File upload for technical drawings
- Integration with CRM systems
- Automated quote generation
- SMS notifications
- Multi-language support
- Advanced filtering and search
