<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-updates-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-web-browser" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-web-browser\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-system-ui" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-system-ui\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-splash-screen\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-secure-store" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-secure-store\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-linking\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-keep-awake\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-json-utils\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-manifests\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-haptics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-haptics\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-font\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="dev-menu-packager-host" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\dev-menu-packager-host"/><file name="EXDevMenuApp.android.js" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\EXDevMenuApp.android.js"/><file name="Inter-Black.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="expo_dev_launcher_android.bundle" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\expo_dev_launcher_android.bundle"/><file name="Inter-Black.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-dev-client\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="app.config" path="D:\Triumph\android-ecom\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out\app.config"/></source></dataSet><dataSet config=":expo-blur" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-blur\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-asset" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo-asset\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-voice_voice" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\@react-native-voice\voice\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_datetimepicker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\@react-native-community\datetimepicker\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\node_modules\expo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Triumph\android-ecom\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>