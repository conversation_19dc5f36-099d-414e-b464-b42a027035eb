1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sohangpurh53.ecommtest"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:5:3-75
11-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:5:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:3:3-64
12-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:3:20-62
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:4:3-77
13-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:4:20-75
14    <uses-permission android:name="android.permission.VIBRATE" />
14-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:6:3-63
14-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:6:20-61
15    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
15-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:7:3-78
15-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:7:20-76
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:8:3-68
16-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:8:20-66
17    <uses-permission android:name="android.permission.MICROPHONE" />
17-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:9:3-66
17-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:9:20-64
18
19    <queries>
19-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:10:3-16:13
20        <intent>
20-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:11:5-15:14
21            <action android:name="android.intent.action.VIEW" />
21-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:7-58
21-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:7-67
23-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:17-65
24
25            <data android:scheme="https" />
25-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:7-37
25-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:13-35
26        </intent>
27
28        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
28-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
28-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
29        <intent>
29-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
30-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
31        </intent>
32        <intent>
32-->[:expo-web-browser] D:\Triumph\android-ecom\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
33
34            <!-- Required for opening tabs if targeting API 30 -->
35            <action android:name="android.support.customtabs.action.CustomTabsService" />
35-->[:expo-web-browser] D:\Triumph\android-ecom\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-90
35-->[:expo-web-browser] D:\Triumph\android-ecom\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-87
36        </intent>
37    </queries>
38
39    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
39-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09fbd29eeff6f06226720f66e439d483\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
39-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09fbd29eeff6f06226720f66e439d483\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
40    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
40-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09fbd29eeff6f06226720f66e439d483\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
40-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\09fbd29eeff6f06226720f66e439d483\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
41
42    <permission
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
43        android:name="com.sohangpurh53.ecommtest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.sohangpurh53.ecommtest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7a0941004ac45c50b486dd320a2bfc96\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
47
48    <application
48-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:3-35:17
49        android:name="com.sohangpurh53.ecommtest.MainApplication"
49-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:16-47
50        android:allowBackup="true"
50-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:162-188
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:377-445
52        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
52-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:307-376
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:fullBackupContent="@xml/secure_store_backup_rules"
55-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:248-306
56        android:icon="@mipmap/ic_launcher"
56-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:81-115
57        android:label="@string/app_name"
57-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:48-80
58        android:roundIcon="@mipmap/ic_launcher_round"
58-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:116-161
59        android:supportsRtl="true"
59-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:221-247
60        android:theme="@style/AppTheme"
60-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:17:189-220
61        android:usesCleartextTraffic="true" >
61-->D:\Triumph\android-ecom\android\app\src\debug\AndroidManifest.xml:6:18-53
62        <meta-data
62-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:18:5-83
63            android:name="expo.modules.updates.ENABLED"
63-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:18:16-59
64            android:value="false" />
64-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:18:60-81
65        <meta-data
65-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:19:5-105
66            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
66-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:19:16-80
67            android:value="ALWAYS" />
67-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:19:81-103
68        <meta-data
68-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:20:5-99
69            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
69-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:20:16-79
70            android:value="0" />
70-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:20:80-97
71
72        <activity
72-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:5-34:16
73            android:name="com.sohangpurh53.ecommtest.MainActivity"
73-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:15-43
74            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
74-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:44-134
75            android:exported="true"
75-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:256-279
76            android:launchMode="singleTask"
76-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:135-166
77            android:screenOrientation="portrait"
77-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:280-316
78            android:theme="@style/Theme.App.SplashScreen"
78-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:210-255
79            android:windowSoftInputMode="adjustResize" >
79-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:21:167-209
80            <intent-filter>
80-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:22:7-25:23
81                <action android:name="android.intent.action.MAIN" />
81-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:23:9-60
81-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:23:17-58
82
83                <category android:name="android.intent.category.LAUNCHER" />
83-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:24:9-68
83-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:24:19-66
84            </intent-filter>
85            <intent-filter>
85-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:26:7-33:23
86                <action android:name="android.intent.action.VIEW" />
86-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:7-58
86-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:15-56
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:28:9-67
88-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:28:19-65
89                <category android:name="android.intent.category.BROWSABLE" />
89-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:7-67
89-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:17-65
90
91                <data android:scheme="myapp" />
91-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:7-37
91-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:13-35
92                <data android:scheme="com.sohangpurh53.ecommtest" />
92-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:7-37
92-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:13-35
93                <data android:scheme="exp+e-comm-test" />
93-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:7-37
93-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:13-35
94            </intent-filter>
95        </activity>
96
97        <provider
97-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
98            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
98-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
99            android:authorities="com.sohangpurh53.ecommtest.fileprovider"
99-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
100            android:exported="false"
100-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
101            android:grantUriPermissions="true" >
101-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
102            <meta-data
102-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
103                android:name="android.support.FILE_PROVIDER_PATHS"
103-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
104                android:resource="@xml/file_provider_paths" />
104-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
105        </provider>
106
107        <activity
107-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
108            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
108-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
109            android:exported="true"
109-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
110            android:launchMode="singleTask"
110-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
111            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
111-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
112            <intent-filter>
112-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
113                <action android:name="android.intent.action.VIEW" />
113-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:7-58
113-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:15-56
114
115                <category android:name="android.intent.category.DEFAULT" />
115-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:28:9-67
115-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:28:19-65
116                <category android:name="android.intent.category.BROWSABLE" />
116-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:7-67
116-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:17-65
117
118                <data android:scheme="expo-dev-launcher" />
118-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:7-37
118-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:13-35
119            </intent-filter>
120        </activity>
121        <activity
121-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
122            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
122-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
123            android:screenOrientation="portrait"
123-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
124            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
124-->[:expo-dev-launcher] D:\Triumph\android-ecom\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
125        <activity
125-->[:expo-dev-menu] D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
126            android:name="expo.modules.devmenu.DevMenuActivity"
126-->[:expo-dev-menu] D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
127            android:exported="true"
127-->[:expo-dev-menu] D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
128            android:launchMode="singleTask"
128-->[:expo-dev-menu] D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
129            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
129-->[:expo-dev-menu] D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
130            <intent-filter>
130-->[:expo-dev-menu] D:\Triumph\android-ecom\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
131                <action android:name="android.intent.action.VIEW" />
131-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:7-58
131-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:12:15-56
132
133                <category android:name="android.intent.category.DEFAULT" />
133-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:28:9-67
133-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:28:19-65
134                <category android:name="android.intent.category.BROWSABLE" />
134-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:7-67
134-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:13:17-65
135
136                <data android:scheme="expo-dev-menu" />
136-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:7-37
136-->D:\Triumph\android-ecom\android\app\src\main\AndroidManifest.xml:14:13-35
137            </intent-filter>
138        </activity>
139
140        <provider
140-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
141            android:name="expo.modules.filesystem.FileSystemFileProvider"
141-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
142            android:authorities="com.sohangpurh53.ecommtest.FileSystemFileProvider"
142-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
143            android:exported="false"
143-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
144            android:grantUriPermissions="true" >
144-->[:expo-file-system] D:\Triumph\android-ecom\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
145            <meta-data
145-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
146                android:name="android.support.FILE_PROVIDER_PATHS"
146-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
147                android:resource="@xml/file_system_provider_paths" />
147-->[:react-native-webview] D:\Triumph\android-ecom\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
148        </provider>
149
150        <meta-data
150-->[:expo-modules-core] D:\Triumph\android-ecom\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
151            android:name="org.unimodules.core.AppLoader#react-native-headless"
151-->[:expo-modules-core] D:\Triumph\android-ecom\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
152            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
152-->[:expo-modules-core] D:\Triumph\android-ecom\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
153        <meta-data
153-->[:expo-modules-core] D:\Triumph\android-ecom\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
154            android:name="com.facebook.soloader.enabled"
154-->[:expo-modules-core] D:\Triumph\android-ecom\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
155            android:value="true" />
155-->[:expo-modules-core] D:\Triumph\android-ecom\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
156
157        <activity
157-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c374240a76a8b2d18933a8725059a3\transformed\jetified-react-android-0.76.9-debug\AndroidManifest.xml:19:9-21:40
158            android:name="com.facebook.react.devsupport.DevSettingsActivity"
158-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c374240a76a8b2d18933a8725059a3\transformed\jetified-react-android-0.76.9-debug\AndroidManifest.xml:20:13-77
159            android:exported="false" />
159-->[com.facebook.react:react-android:0.76.9] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32c374240a76a8b2d18933a8725059a3\transformed\jetified-react-android-0.76.9-debug\AndroidManifest.xml:21:13-37
160
161        <provider
161-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7b46e915d1d543deb7d7858bf15ecf8\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
162            android:name="androidx.startup.InitializationProvider"
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7b46e915d1d543deb7d7858bf15ecf8\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
163            android:authorities="com.sohangpurh53.ecommtest.androidx-startup"
163-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7b46e915d1d543deb7d7858bf15ecf8\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
164            android:exported="false" >
164-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7b46e915d1d543deb7d7858bf15ecf8\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
165            <meta-data
165-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7b46e915d1d543deb7d7858bf15ecf8\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
166                android:name="androidx.emoji2.text.EmojiCompatInitializer"
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7b46e915d1d543deb7d7858bf15ecf8\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
167                android:value="androidx.startup" />
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d7b46e915d1d543deb7d7858bf15ecf8\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
168            <meta-data
168-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4302bd554da0ac1f6928cfb459f1ec7f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
169-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4302bd554da0ac1f6928cfb459f1ec7f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
170                android:value="androidx.startup" />
170-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4302bd554da0ac1f6928cfb459f1ec7f\transformed\jetified-lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
171            <meta-data
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
173                android:value="androidx.startup" />
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
174        </provider>
175
176        <receiver
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
177            android:name="androidx.profileinstaller.ProfileInstallReceiver"
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
178            android:directBootAware="false"
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
179            android:enabled="true"
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
180            android:exported="true"
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
181            android:permission="android.permission.DUMP" >
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
182            <intent-filter>
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
183                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
184            </intent-filter>
185            <intent-filter>
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
186                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
187            </intent-filter>
188            <intent-filter>
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
189                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
190            </intent-filter>
191            <intent-filter>
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
192                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b5804acd4f84445951670cb1b9843e70\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
193            </intent-filter>
194        </receiver>
195    </application>
196
197</manifest>
