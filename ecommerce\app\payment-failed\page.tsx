"use client";
import { useRouter, useSearchParams } from "next/navigation";
import MainHOF from "../../layout/MainHOF";
import { Button } from "../../components/ui/button";
import { AlertCircle } from "lucide-react";
import { Suspense } from "react";

const PaymentFailedContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get("order_id");

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <div className="flex justify-center mb-6">
            <AlertCircle className="h-16 w-16 text-red-500" />
          </div>
          <h1 className="text-2xl font-bold mb-4">Payment Failed</h1>
          <p className="text-muted-foreground mb-8">
            We couldn't process your payment. Your order has been saved, and you can try again.
          </p>

          <div className="space-y-4">
            {orderId && (
              <Button
                className="w-full"
                onClick={() => router.push(`/checkout?retry_order=${orderId}`)}
              >
                Try Again
              </Button>
            )}
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push("/")}
            >
              Return to Home
            </Button>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

const PaymentFailed = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentFailedContent />
    </Suspense>
  );
};

export default PaymentFailed;
