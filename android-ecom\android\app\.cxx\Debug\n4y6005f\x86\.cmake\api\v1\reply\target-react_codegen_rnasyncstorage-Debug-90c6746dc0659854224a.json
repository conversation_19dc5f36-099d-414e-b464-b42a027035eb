{"artifacts": [{"path": "rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o"}, {"path": "rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./react/renderer/components/rnasyncstorage/EventEmitters.cpp.o"}, {"path": "rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./react/renderer/components/rnasyncstorage/Props.cpp.o"}, {"path": "rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o"}, {"path": "rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./react/renderer/components/rnasyncstorage/States.cpp.o"}, {"path": "rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o"}, {"path": "rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./rnasyncstorage-generated.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_include_directories", "target_link_libraries"], "files": ["D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "includes": [{"backtrace": 3, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 3, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/6118f662953cfd7a4a508c73e43e201c/transformed/jetified-fbjni-0.6.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/32c374240a76a8b2d18933a8725059a3/transformed/jetified-react-android-0.76.9-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.10.2/transforms/32c374240a76a8b2d18933a8725059a3/transformed/jetified-react-android-0.76.9-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "name": "react_codegen_rnasyncstorage", "paths": {"build": "rnasyncstorage_autolinked_build", "source": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/Triumph/android-ecom/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}