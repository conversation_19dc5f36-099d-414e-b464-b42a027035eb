{"logs": [{"outputFile": "com.sohangpurh53.ecommtest.app-mergeDebugResources-62:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,336,337,344,348,553,556", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,24655,24771,25228,25522,37715,37887", "endLines": "2,17,18,19,336,337,344,348,555,560", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,24766,24892,25349,25645,37882,38234"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,49,52,57,68,74,84,94,104,114,124,134,144,154,164,174,184,194,204,214,224,230,236,242,248,252,256,257,258,259,263,266,269,272,275,278,281,285,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,2052,2311,2582,2800,3032,3268,3518,3731,3962,4078,4248,4569,5598,6055,6608,7165,7723,8286,8840,9393,9947,10502,11053,11608,12166,12723,13271,13827,14384,14726,15070,15420,15770,16099,16440,16578,16722,16878,17271,17489,17711,17937,18153,18323,18513,18754,19013", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,23,26,29,32,35,38,41,44,47,48,51,56,67,73,83,93,103,113,123,133,143,153,163,173,183,193,203,213,223,229,235,241,247,251,255,256,257,258,262,265,268,271,274,277,280,284,288,291", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,2047,2306,2577,2795,3027,3263,3513,3726,3957,4073,4243,4564,5593,6050,6603,7160,7718,8281,8835,9388,9942,10497,11048,11603,12161,12718,13266,13822,14379,14721,15065,15415,15765,16094,16435,16573,16717,16873,17266,17484,17706,17932,18148,18318,18508,18749,19008,19185"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,80,81,82,83,85,86,89,92,187,190,193,196,202,205,272,273,276,281,292,352,362,372,382,392,402,412,422,432,442,452,462,472,482,492,502,508,514,520,526,530,534,535,536,537,541,544,547,550,561,564,567,571,575", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,6970,7063,7170,7275,7497,7622,7835,8094,14275,14493,14725,14961,15410,15623,20410,20526,20696,21017,22046,25817,26370,26927,27485,28048,28602,29155,29709,30264,30815,31370,31928,32485,33033,33589,34146,34438,34732,35032,35332,35661,36002,36140,36284,36440,36833,37051,37273,37499,38239,38409,38599,38840,39099", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,80,81,82,83,85,88,91,94,189,192,195,198,204,207,272,275,280,291,297,361,371,381,391,401,411,421,431,441,451,461,471,481,491,501,507,513,519,525,529,533,534,535,536,540,543,546,549,552,563,566,570,574,577", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,7058,7165,7270,7389,7617,7830,8089,8360,14488,14720,14956,15206,15618,15849,20521,20691,21012,22041,22498,26365,26922,27480,28043,28597,29150,29704,30259,30810,31365,31923,32480,33028,33584,34141,34433,34727,35027,35327,35656,35997,36135,36279,36435,36828,37046,37268,37494,37710,38404,38594,38835,39094,39271"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,35,37,38,39,40,42,44,45,46,47,48,50,52,54,56,58,60,61,66,68,70,71,72,74,76,77,78,79,84,95,138,141,184,199,208,210,212,214,217,221,224,225,226,229,230,231,232,233,234,237,238,240,242,244,246,250,252,253,254,255,257,261,263,265,266,267,268,269,270,298,299,300,310,311,312,324", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1603,1694,1797,1900,2005,2112,2221,2330,2439,2548,2657,2764,2867,2986,3141,3296,3401,3522,3623,3770,3911,4014,4133,4240,4343,4498,4669,4818,4983,5140,5291,5410,5761,5910,6059,6171,6318,6471,6618,6693,6782,6869,7394,8365,11123,11308,14078,15211,15854,15977,16100,16213,16396,16651,16852,16941,17052,17285,17386,17481,17604,17733,17850,18027,18126,18261,18404,18539,18658,18859,18978,19071,19182,19238,19345,19540,19651,19784,19879,19970,20061,20154,20271,22503,22574,22657,23280,23337,23395,24019", "endLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,34,36,37,38,39,41,43,44,45,46,47,49,51,53,55,57,59,60,65,67,69,70,71,73,75,76,77,78,79,84,137,140,183,186,201,209,211,213,216,220,223,224,225,228,229,230,231,232,233,236,237,239,241,243,245,249,251,252,253,254,256,260,262,264,265,266,267,268,269,271,298,299,309,310,311,323,335", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1689,1792,1895,2000,2107,2216,2325,2434,2543,2652,2759,2862,2981,3136,3291,3396,3517,3618,3765,3906,4009,4128,4235,4338,4493,4664,4813,4978,5135,5286,5405,5756,5905,6054,6166,6313,6466,6613,6688,6777,6864,6965,7492,11118,11303,14073,14270,15405,15972,16095,16208,16391,16646,16847,16936,17047,17280,17381,17476,17599,17728,17845,18022,18121,18256,18399,18534,18653,18854,18973,19066,19177,19233,19340,19535,19646,19779,19874,19965,20056,20149,20266,20405,22569,22652,23275,23332,23390,24014,24650"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9b684815059565a38e7c140c7bc9dc68\\transformed\\media-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "338,341,345,349", "startColumns": "4,4,4,4", "startOffsets": "24897,25065,25354,25650", "endLines": "340,343,347,351", "endColumns": "12,12,12,12", "endOffsets": "25060,25223,25517,25812"}}]}]}