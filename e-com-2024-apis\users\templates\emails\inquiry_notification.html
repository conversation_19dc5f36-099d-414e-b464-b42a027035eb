<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Product Inquiry</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #dc3545; color: white; padding: 20px; text-align: center; border-radius: 5px; }
        .content { padding: 20px 0; }
        .section { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .section h3 { margin-top: 0; color: #495057; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .btn { display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        .urgent { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 New Product Inquiry</h1>
            <p>Received on {{ submission_date }}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h3>👤 Customer Details</h3>
                <p><strong>Name:</strong> {{ customer_name }}</p>
                <p><strong>Email:</strong> {{ customer_email }}</p>
                {% if customer_phone %}<p><strong>Phone:</strong> {{ customer_phone }}</p>{% endif %}
                {% if customer_company %}<p><strong>Company:</strong> {{ customer_company }}</p>{% endif %}
            </div>
            
            <div class="section">
                <h3>📦 Product Details</h3>
                <p><strong>Product:</strong> {{ product_name }}</p>
                <p><strong>Product Slug:</strong> {{ product_slug }}</p>
            </div>
            
            <div class="section">
                <h3>📋 Inquiry Details</h3>
                <p><strong>Inquiry ID:</strong> {{ inquiry_id }}</p>
                {% if color %}<p><strong>Color:</strong> {{ color }}</p>{% endif %}
                {% if finish %}<p><strong>Finish:</strong> {{ finish }}</p>{% endif %}
                {% if size %}<p><strong>Size:</strong> {{ size }}</p>{% endif %}
                {% if thickness %}<p><strong>Thickness:</strong> {{ thickness }}</p>{% endif %}
                <p><strong>Quantity:</strong> {{ quantity }}</p>
            </div>
            
            {% if message %}
            <div class="section">
                <h3>💬 Customer Message</h3>
                <p>{{ message }}</p>
            </div>
            {% endif %}
            
            <div class="section">
                <h3>⚡ Admin Actions</h3>
                <p class="urgent">Please respond to the customer within 24-48 hours.</p>
                <a href="{{ admin_url }}" class="btn">View/Edit Inquiry</a>
            </div>
        </div>
        
        <div class="footer">
            <p>This is an automated notification from the e-commerce system.</p>
        </div>
    </div>
</body>
</html>
