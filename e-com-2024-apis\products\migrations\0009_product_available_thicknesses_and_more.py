# Generated by Django 5.0.2 on 2025-08-26 13:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0008_productmedia_productvariantmedia_productvariantvideo_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='available_thicknesses',
            field=models.JSONField(blank=True, default=list, help_text="List of available thicknesses for edge banding products (e.g., ['0.5mm', '1mm', '2mm'])"),
        ),
        migrations.AddField(
            model_name='product',
            name='is_edge_banding',
            field=models.BooleanField(default=False, help_text='Mark as true if this is an edge banding product that requires inquiry'),
        ),
        migrations.CreateModel(
            name='ProductInquiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Customer name', max_length=100)),
                ('email', models.EmailField(help_text='Customer email', max_length=254)),
                ('phone', models.CharField(blank=True, help_text='Customer phone number (optional)', max_length=20)),
                ('company', models.CharField(blank=True, help_text='Company name (optional)', max_length=200)),
                ('color', models.CharField(blank=True, help_text='Requested color', max_length=100)),
                ('size', models.CharField(blank=True, help_text='Requested size/dimensions', max_length=100)),
                ('thickness', models.CharField(blank=True, help_text='Requested thickness (e.g., 0.5mm, 1mm, 2mm)', max_length=50)),
                ('quantity', models.PositiveIntegerField(default=1, help_text='Requested quantity')),
                ('message', models.TextField(blank=True, help_text='Additional message or requirements')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('contacted', 'Customer Contacted'), ('quoted', 'Quote Sent'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='pending', help_text='Inquiry status', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('admin_notes', models.TextField(blank=True, help_text='Internal notes for admin use')),
                ('product', models.ForeignKey(help_text='Product being inquired about', on_delete=django.db.models.deletion.CASCADE, related_name='inquiries', to='products.product')),
            ],
            options={
                'verbose_name': 'Product Inquiry',
                'verbose_name_plural': 'Product Inquiries',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['product', 'status'], name='products_pr_product_0bae5d_idx'), models.Index(fields=['created_at'], name='products_pr_created_103cab_idx'), models.Index(fields=['email'], name='products_pr_email_2e403d_idx')],
            },
        ),
    ]
