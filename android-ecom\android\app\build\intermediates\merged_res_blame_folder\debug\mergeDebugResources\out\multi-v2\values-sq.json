{"logs": [{"outputFile": "com.sohangpurh53.ecommtest.app-mergeDebugResources-62:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,222,308,409,535,618,683,783,853,912,1010,1074,1133,1205,1268,1322,1439,1496,1558,1612,1684,1819,1902,1980,2091,2175,2257,2347,2414,2480,2551,2630,2718,2794,2872,2944,3017,3106,3178,3272,3371,3445,3517,3618,3668,3734,3824,3913,3975,4039,4102,4218,4326,4435,4544", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,110,83,81,89,66,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,82", "endOffsets": "217,303,404,530,613,678,778,848,907,1005,1069,1128,1200,1263,1317,1434,1491,1553,1607,1679,1814,1897,1975,2086,2170,2252,2342,2409,2475,2546,2625,2713,2789,2867,2939,3012,3101,3173,3267,3366,3440,3512,3613,3663,3729,3819,3908,3970,4034,4097,4213,4321,4430,4539,4622"}, "to": {"startLines": "2,34,42,43,44,54,55,70,75,77,78,79,80,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3042,3865,3966,4092,5052,5117,6929,7324,7452,7550,7614,7673,7745,7808,7862,7979,8036,8098,8152,8224,8592,8675,8753,8864,8948,9030,9120,9187,9253,9324,9403,9491,9567,9645,9717,9790,9879,9951,10045,10144,10218,10290,10391,10441,10507,10597,10686,10748,10812,10875,10991,11099,11208,11499", "endLines": "5,34,42,43,44,54,55,70,75,77,78,79,80,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,127", "endColumns": "12,85,100,125,82,64,99,69,58,97,63,58,71,62,53,116,56,61,53,71,134,82,77,110,83,81,89,66,65,70,78,87,75,77,71,72,88,71,93,98,73,71,100,49,65,89,88,61,63,62,115,107,108,108,82", "endOffsets": "267,3123,3961,4087,4170,5112,5212,6994,7378,7545,7609,7668,7740,7803,7857,7974,8031,8093,8147,8219,8354,8670,8748,8859,8943,9025,9115,9182,9248,9319,9398,9486,9562,9640,9712,9785,9874,9946,10040,10139,10213,10285,10386,10436,10502,10592,10681,10743,10807,10870,10986,11094,11203,11312,11577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09fbd29eeff6f06226720f66e439d483\\transformed\\biometric-1.1.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,261,386,525,668,802,937,1081,1177,1320,1468", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "163,256,381,520,663,797,932,1076,1172,1315,1463,1584"}, "to": {"startLines": "48,51,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4434,4761,5530,5655,5794,5937,6071,6206,6350,6446,6589,6737", "endColumns": "112,92,124,138,142,133,134,143,95,142,147,120", "endOffsets": "4542,4849,5650,5789,5932,6066,6201,6345,6441,6584,6732,6853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "35,36,37,38,39,40,41,147", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3128,3227,3329,3427,3524,3632,3743,13105", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3222,3324,3422,3519,3627,3738,3860,13201"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,210,281,350,432,501,568,650,734,823,906,976,1062,1151,1226,1307,1388,1465,1540,1613,1700,1777,1858,1932", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "124,205,276,345,427,496,563,645,729,818,901,971,1057,1146,1221,1302,1383,1460,1535,1608,1695,1772,1853,1927,2010"}, "to": {"startLines": "33,47,69,71,72,76,89,90,91,128,129,132,133,136,137,138,140,141,143,145,146,148,151,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2968,4353,6858,6999,7068,7383,8359,8426,8508,11582,11671,11932,12002,12243,12332,12407,12563,12644,12797,12945,13018,13206,13426,13628,13702", "endColumns": "73,80,70,68,81,68,66,81,83,88,82,69,85,88,74,80,80,76,74,72,86,76,80,73,82", "endOffsets": "3037,4429,6924,7063,7145,7447,8421,8503,8587,11666,11749,11997,12083,12327,12402,12483,12639,12716,12867,13013,13100,13278,13502,13697,13780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf233ddec2dc68e89587520b037b44b2\\transformed\\jetified-foundation-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "155,156", "startColumns": "4,4", "startOffsets": "13785,13883", "endColumns": "97,98", "endOffsets": "13878,13977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6e705899487cb68772be9da92e21a0c\\transformed\\browser-1.6.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "49,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4547,5217,5318,5429", "endColumns": "114,100,110,100", "endOffsets": "4657,5313,5424,5525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65209923ef4068f1ffed3bedec62176\\transformed\\jetified-ui-release\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,382,484,580,661,754,846,936,1023,1114,1187,1262,1338,1411,1488,1554", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,74,75,72,76,65,120", "endOffsets": "195,278,377,479,575,656,749,841,931,1018,1109,1182,1257,1333,1406,1483,1549,1670"}, "to": {"startLines": "45,46,50,52,53,73,74,125,126,130,131,135,139,142,144,149,150,152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4175,4270,4662,4854,4956,7150,7231,11317,11409,11754,11841,12170,12488,12721,12872,13283,13360,13507", "endColumns": "94,82,98,101,95,80,92,91,89,86,90,72,74,75,72,76,65,120", "endOffsets": "4265,4348,4756,4951,5047,7226,7319,11404,11494,11836,11927,12238,12558,12792,12940,13355,13421,13623"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "272,386,486,598,684,790,913,995,1073,1164,1257,1352,1446,1547,1640,1735,1832,1923,2016,2097,2203,2307,2405,2511,2615,2717,2871,12088", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "381,481,593,679,785,908,990,1068,1159,1252,1347,1441,1542,1635,1730,1827,1918,2011,2092,2198,2302,2400,2506,2610,2712,2866,2963,12165"}}]}]}