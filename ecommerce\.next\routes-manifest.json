{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Content-Security-Policy", "value": "default-src 'self' 'unsafe-eval' 'unsafe-inline'; img-src 'self' data: https: http:; media-src 'self' https://minio-triumph.trio.net.in; connect-src 'self' http://localhost:* https://api-ecom.trio.net.in https://api-ecom.dev.trio.net.in https://api.phonepe.com https://accounts.google.com;"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}], "regex": "^(?:\\/(.*))(?:\\/)?$"}, {"source": "/:path*.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Cache-Control", "value": "public, max-age=3600, stale-while-revalidate=86400"}], "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?\\.xml(?:\\/)?$"}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Cache-Control", "value": "public, max-age=3600, stale-while-revalidate=86400"}], "regex": "^\\/sitemap\\.xml(?:\\/)?$"}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}, {"key": "Cache-Control", "value": "public, max-age=3600, stale-while-revalidate=86400"}], "regex": "^\\/robots\\.txt(?:\\/)?$"}]}