"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  Home, 
  Search, 
  ShoppingBag, 
  ArrowLeft, 
  Sparkles,
  Package,
  Heart,
  Star,
  TrendingUp
} from 'lucide-react';
import { But<PERSON> } from '../components/ui/button';
import { Input } from '../components/ui/input';
import MainHOF from '../layout/MainHOF';
import '../styles/not-found.css';

interface PopularProduct {
  id: string;
  name: string;
  price: string;
  image: string;
  category: string;
  rating: number;
}

const NotFoundPage = () => {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Mock popular products - in real app, fetch from API
  const popularProducts: PopularProduct[] = [
    {
      id: '1',
      name: 'Smart Door Lock',
      price: '₹2,999',
      image: '/assets/door-lock.jpg',
      category: 'Security',
      rating: 4.5
    },
    {
      id: '2',
      name: 'Digital Safe',
      price: '₹5,499',
      image: '/assets/digital-safe.jpg',
      category: 'Security',
      rating: 4.8
    },
    {
      id: '3',
      name: 'Hardware Tools Set',
      price: '₹1,299',
      image: '/assets/tools-set.jpg',
      category: 'Tools',
      rating: 4.3
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/shop?search=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleGoHome = () => {
    router.push('/');
  };

  const handleGoToShop = () => {
    router.push('/shop');
  };

  if (isLoading) {
    return (
      <MainHOF>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-theme-accent-primary"></div>
        </div>
      </MainHOF>
    );
  }

  return (
    <MainHOF>
      <div className="not-found-container min-h-screen bg-gradient-to-br from-theme-homepage via-white to-gray-50 flex flex-col items-center justify-center px-4 py-8 relative overflow-hidden">
        
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="not-found-icon absolute top-20 left-10 opacity-10">
            <Package className="h-16 w-16 text-theme-accent-primary animate-float" />
          </div>
          <div className="not-found-icon absolute top-40 right-20 opacity-10" style={{ animationDelay: '1s' }}>
            <ShoppingBag className="h-12 w-12 text-theme-accent-secondary animate-float" />
          </div>
          <div className="not-found-icon absolute bottom-40 left-20 opacity-10" style={{ animationDelay: '2s' }}>
            <Heart className="h-14 w-14 text-theme-out-of-stock animate-float" />
          </div>
          <div className="not-found-icon absolute bottom-20 right-10 opacity-10" style={{ animationDelay: '0.5s' }}>
            <Sparkles className="h-10 w-10 text-theme-accent-primary animate-float" />
          </div>
        </div>

        {/* Main content */}
        <div className="not-found-card max-w-4xl w-full text-center space-y-8 relative z-10">
          
          {/* 404 Text */}
          <div className="space-y-4">
            <h1 className="not-found-404-text text-8xl md:text-9xl font-black tracking-tight">
              404
            </h1>
            <h2 className="text-2xl md:text-3xl font-bold text-theme-text-primary mb-2">
              Oops! Page Not Found
            </h2>
            <p className="text-lg text-theme-text-secondary max-w-2xl mx-auto leading-relaxed">
              The page you're looking for seems to have wandered off. Don't worry, even the best explorers sometimes take a wrong turn!
            </p>
          </div>

          {/* Search Section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-theme-text-primary mb-4 flex items-center justify-center gap-2">
              <Search className="h-5 w-5" />
              Search Our Products
            </h3>
            <form onSubmit={handleSearch} className="flex gap-2">
              <Input
                type="text"
                placeholder="What are you looking for?"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 border-theme-accent-primary/20 focus:border-theme-accent-primary"
              />
              <Button 
                type="submit" 
                className="bg-theme-accent-primary hover:bg-theme-accent-hover text-white px-6"
              >
                <Search className="h-4 w-4" />
              </Button>
            </form>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              onClick={handleGoHome}
              className="bg-theme-accent-primary hover:bg-theme-accent-hover text-white px-8 py-3 rounded-xl flex items-center gap-2 transition-all duration-300 hover:scale-105"
            >
              <Home className="h-5 w-5" />
              Go Home
            </Button>
            <Button
              onClick={handleGoToShop}
              variant="outline"
              className="border-theme-accent-primary text-theme-accent-primary hover:bg-theme-accent-primary hover:text-white px-8 py-3 rounded-xl flex items-center gap-2 transition-all duration-300 hover:scale-105"
            >
              <ShoppingBag className="h-5 w-5" />
              Browse Shop
            </Button>
          </div>

          {/* Popular Products Section */}
          <div className="mt-12 w-full">
            <h3 className="text-xl font-bold text-theme-text-primary mb-6 flex items-center justify-center gap-2">
              <TrendingUp className="h-5 w-5 text-theme-accent-primary" />
              Popular Products
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {popularProducts.map((product) => (
                <Link
                  key={product.id}
                  href={`/product/${product.id}`}
                  className="not-found-popular-item group"
                >
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/30 hover:shadow-xl transition-all duration-300">
                    <div className="aspect-square bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                      <Package className="h-12 w-12 text-theme-accent-primary/50" />
                    </div>
                    <h4 className="font-semibold text-theme-text-primary text-sm mb-1 group-hover:text-theme-accent-primary transition-colors">
                      {product.name}
                    </h4>
                    <p className="text-xs text-theme-text-secondary mb-2">{product.category}</p>
                    <div className="flex items-center justify-between">
                      <span className="font-bold text-theme-accent-primary">{product.price}</span>
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs text-theme-text-secondary">{product.rating}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className="mt-8 pt-8 border-t border-gray-200/50">
            <p className="text-sm text-theme-text-secondary mb-4">Quick Links:</p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/shop" className="text-theme-accent-primary hover:text-theme-accent-hover text-sm font-medium transition-colors">
                All Products
              </Link>
              <Link href="/cart" className="text-theme-accent-primary hover:text-theme-accent-hover text-sm font-medium transition-colors">
                Shopping Cart
              </Link>
              <Link href="/account" className="text-theme-accent-primary hover:text-theme-accent-hover text-sm font-medium transition-colors">
                My Account
              </Link>
              <Link href="/contact-us" className="text-theme-accent-primary hover:text-theme-accent-hover text-sm font-medium transition-colors">
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MainHOF>
  );
};

export default NotFoundPage;
