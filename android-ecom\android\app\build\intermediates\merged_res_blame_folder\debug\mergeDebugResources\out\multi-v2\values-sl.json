{"logs": [{"outputFile": "com.sohangpurh53.ecommtest.app-mergeDebugResources-62:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09fbd29eeff6f06226720f66e439d483\\transformed\\biometric-1.1.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,253,374,512,642,768,893,1033,1132,1275,1409", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "157,248,369,507,637,763,888,1028,1127,1270,1404,1537"}, "to": {"startLines": "50,53,61,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4572,4881,5633,5754,5892,6022,6148,6273,6413,6512,6655,6789", "endColumns": "106,90,120,137,129,125,124,139,98,142,133,132", "endOffsets": "4674,4967,5749,5887,6017,6143,6268,6408,6507,6650,6784,6917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65209923ef4068f1ffed3bedec62176\\transformed\\jetified-ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,980,1066,1138,1215,1295,1373,1451,1521", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,975,1061,1133,1210,1290,1368,1446,1516,1637"}, "to": {"startLines": "47,48,52,54,55,75,76,127,128,132,133,137,141,144,146,151,152,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4312,4407,4785,4972,5070,7219,7296,11305,11397,11721,11803,12119,12432,12667,12825,13239,13317,13469", "endColumns": "94,80,95,97,84,76,86,91,81,81,85,71,76,79,77,77,69,120", "endOffsets": "4402,4483,4876,5065,5150,7291,7378,11392,11474,11798,11884,12186,12504,12742,12898,13312,13382,13585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,322,410,516,642,726,792,886,962,1025,1137,1202,1256,1326,1386,1442,1554,1611,1673,1729,1802,1936,2021,2106,2219,2303,2386,2475,2542,2608,2681,2758,2842,2916,2992,3067,3140,3228,3301,3391,3482,3554,3628,3719,3771,3838,3922,4009,4071,4135,4198,4301,4398,4496,4593", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,87,105,125,83,65,93,75,62,111,64,53,69,59,55,111,56,61,55,72,133,84,84,112,83,82,88,66,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,76", "endOffsets": "317,405,511,637,721,787,881,957,1020,1132,1197,1251,1321,1381,1437,1549,1606,1668,1724,1797,1931,2016,2101,2214,2298,2381,2470,2537,2603,2676,2753,2837,2911,2987,3062,3135,3223,3296,3386,3477,3549,3623,3714,3766,3833,3917,4004,4066,4130,4193,4296,4393,4491,4588,4665"}, "to": {"startLines": "2,36,44,45,46,56,57,72,77,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3185,3996,4102,4228,5155,5221,6993,7383,7517,7629,7694,7748,7818,7878,7934,8046,8103,8165,8221,8294,8648,8733,8818,8931,9015,9098,9187,9254,9320,9393,9470,9554,9628,9704,9779,9852,9940,10013,10103,10194,10266,10340,10431,10483,10550,10634,10721,10783,10847,10910,11013,11110,11208,11479", "endLines": "7,36,44,45,46,56,57,72,77,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "endColumns": "12,87,105,125,83,65,93,75,62,111,64,53,69,59,55,111,56,61,55,72,133,84,84,112,83,82,88,66,65,72,76,83,73,75,74,72,87,72,89,90,71,73,90,51,66,83,86,61,63,62,102,96,97,96,76", "endOffsets": "367,3268,4097,4223,4307,5216,5310,7064,7441,7624,7689,7743,7813,7873,7929,8041,8098,8160,8216,8289,8423,8728,8813,8926,9010,9093,9182,9249,9315,9388,9465,9549,9623,9699,9774,9847,9935,10008,10098,10189,10261,10335,10426,10478,10545,10629,10716,10778,10842,10905,11008,11105,11203,11300,11551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6e705899487cb68772be9da92e21a0c\\transformed\\browser-1.6.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4679,5315,5419,5531", "endColumns": "105,103,111,101", "endOffsets": "4780,5414,5526,5628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "372,484,586,694,781,884,1003,1084,1162,1254,1348,1443,1537,1632,1726,1822,1922,2014,2106,2190,2298,2406,2506,2619,2727,2832,3012,12035", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "479,581,689,776,879,998,1079,1157,1249,1343,1438,1532,1627,1721,1817,1917,2009,2101,2185,2293,2401,2501,2614,2722,2827,3007,3107,12114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,212,283,352,433,504,571,641,724,807,889,961,1035,1117,1194,1276,1358,1434,1512,1589,1673,1747,1829,1901", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "123,207,278,347,428,499,566,636,719,802,884,956,1030,1112,1189,1271,1353,1429,1507,1584,1668,1742,1824,1896,1978"}, "to": {"startLines": "35,49,71,73,74,78,91,92,93,130,131,134,135,138,139,140,142,143,145,147,148,150,153,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3112,4488,6922,7069,7138,7446,8428,8495,8565,11556,11639,11889,11961,12191,12273,12350,12509,12591,12747,12903,12980,13165,13387,13590,13662", "endColumns": "72,83,70,68,80,70,66,69,82,82,81,71,73,81,76,81,81,75,77,76,83,73,81,71,81", "endOffsets": "3180,4567,6988,7133,7214,7512,8490,8560,8643,11634,11716,11956,12030,12268,12345,12427,12586,12662,12820,12975,13059,13234,13464,13657,13739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "37,38,39,40,41,42,43,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3273,3370,3472,3570,3674,3777,3879,13064", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3365,3467,3565,3669,3772,3874,3991,13160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf233ddec2dc68e89587520b037b44b2\\transformed\\jetified-foundation-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,90", "endOffsets": "140,231"}, "to": {"startLines": "157,158", "startColumns": "4,4", "startOffsets": "13744,13834", "endColumns": "89,90", "endOffsets": "13829,13920"}}]}]}