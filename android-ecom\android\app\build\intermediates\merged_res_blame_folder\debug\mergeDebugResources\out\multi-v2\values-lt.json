{"logs": [{"outputFile": "com.sohangpurh53.ecommtest.app-mergeDebugResources-62:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf233ddec2dc68e89587520b037b44b2\\transformed\\jetified-foundation-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,87", "endOffsets": "138,226"}, "to": {"startLines": "157,158", "startColumns": "4,4", "startOffsets": "14040,14128", "endColumns": "87,87", "endOffsets": "14123,14211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\32c374240a76a8b2d18933a8725059a3\\transformed\\jetified-react-android-0.76.9-debug\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,213,286,357,444,514,582,660,742,824,905,979,1062,1146,1224,1307,1390,1466,1542,1616,1713,1788,1870,1943", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "123,208,281,352,439,509,577,655,737,819,900,974,1057,1141,1219,1302,1385,1461,1537,1611,1708,1783,1865,1938,2018"}, "to": {"startLines": "35,49,71,73,74,78,91,92,93,130,131,134,135,138,139,140,142,143,145,147,148,150,153,155,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3149,4531,7035,7186,7257,7577,8590,8658,8736,11811,11893,12148,12222,12464,12548,12626,12786,12869,13022,13173,13247,13445,13683,13887,13960", "endColumns": "72,84,72,70,86,69,67,77,81,81,80,73,82,83,77,82,82,75,75,73,96,74,81,72,79", "endOffsets": "3217,4611,7103,7252,7339,7642,8653,8731,8813,11888,11969,12217,12300,12543,12621,12704,12864,12940,13093,13242,13339,13515,13760,13955,14035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6e705899487cb68772be9da92e21a0c\\transformed\\browser-1.6.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "51,58,59,60", "startColumns": "4,4,4,4", "startOffsets": "4728,5388,5493,5607", "endColumns": "104,104,113,105", "endOffsets": "4828,5488,5602,5708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "37,38,39,40,41,42,43,149", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3305,3403,3513,3612,3715,3826,3936,13344", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3398,3508,3607,3710,3821,3931,4051,13440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09fbd29eeff6f06226720f66e439d483\\transformed\\biometric-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,259,385,526,665,795,927,1064,1161,1316,1459", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "162,254,380,521,660,790,922,1059,1156,1311,1454,1576"}, "to": {"startLines": "50,53,61,62,63,64,65,66,67,68,69,70", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4616,4931,5713,5839,5980,6119,6249,6381,6518,6615,6770,6913", "endColumns": "111,91,125,140,138,129,131,136,96,154,142,121", "endOffsets": "4723,5018,5834,5975,6114,6244,6376,6513,6610,6765,6908,7030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,330,413,509,627,711,777,876,954,1019,1129,1201,1260,1334,1395,1449,1573,1634,1696,1750,1828,1962,2050,2134,2245,2324,2408,2505,2572,2638,2713,2792,2880,2956,3034,3107,3184,3271,3352,3442,3534,3606,3687,3779,3834,3900,3985,4072,4134,4198,4261,4372,4487,4588,4702", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,82,95,117,83,65,98,77,64,109,71,58,73,60,53,123,60,61,53,77,133,87,83,110,78,83,96,66,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,81", "endOffsets": "325,408,504,622,706,772,871,949,1014,1124,1196,1255,1329,1390,1444,1568,1629,1691,1745,1823,1957,2045,2129,2240,2319,2403,2500,2567,2633,2708,2787,2875,2951,3029,3102,3179,3266,3347,3437,3529,3601,3682,3774,3829,3895,3980,4067,4129,4193,4256,4367,4482,4583,4697,4779"}, "to": {"startLines": "2,36,44,45,46,56,57,72,77,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3222,4056,4152,4270,5223,5289,7108,7512,7647,7757,7829,7888,7962,8023,8077,8201,8262,8324,8378,8456,8818,8906,8990,9101,9180,9264,9361,9428,9494,9569,9648,9736,9812,9890,9963,10040,10127,10208,10298,10390,10462,10543,10635,10690,10756,10841,10928,10990,11054,11117,11228,11343,11444,11729", "endLines": "7,36,44,45,46,56,57,72,77,79,80,81,82,83,84,85,86,87,88,89,90,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,129", "endColumns": "12,82,95,117,83,65,98,77,64,109,71,58,73,60,53,123,60,61,53,77,133,87,83,110,78,83,96,66,65,74,78,87,75,77,72,76,86,80,89,91,71,80,91,54,65,84,86,61,63,62,110,114,100,113,81", "endOffsets": "375,3300,4147,4265,4349,5284,5383,7181,7572,7752,7824,7883,7957,8018,8072,8196,8257,8319,8373,8451,8585,8901,8985,9096,9175,9259,9356,9423,9489,9564,9643,9731,9807,9885,9958,10035,10122,10203,10293,10385,10457,10538,10630,10685,10751,10836,10923,10985,11049,11112,11223,11338,11439,11553,11806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65209923ef4068f1ffed3bedec62176\\transformed\\jetified-ui-release\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,380,485,580,657,748,835,919,1005,1093,1168,1245,1322,1397,1477,1560", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "193,277,375,480,575,652,743,830,914,1000,1088,1163,1240,1317,1392,1472,1555,1677"}, "to": {"startLines": "47,48,52,54,55,75,76,127,128,132,133,137,141,144,146,151,152,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4354,4447,4833,5023,5128,7344,7421,11558,11645,11974,12060,12389,12709,12945,13098,13520,13600,13765", "endColumns": "92,83,97,104,94,76,90,86,83,85,87,74,76,76,74,79,82,121", "endOffsets": "4442,4526,4926,5123,5218,7416,7507,11640,11724,12055,12143,12459,12781,13017,13168,13595,13678,13882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,912,1006,1102,1199,1295,1398,1494,1592,1688,1782,1876,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "216,320,433,520,622,744,827,907,1001,1097,1194,1290,1393,1489,1587,1683,1777,1871,1954,2063,2171,2271,2381,2486,2592,2768,2869,2953"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "380,496,600,713,800,902,1024,1107,1187,1281,1377,1474,1570,1673,1769,1867,1963,2057,2151,2234,2343,2451,2551,2661,2766,2872,3048,12305", "endColumns": "115,103,112,86,101,121,82,79,93,95,96,95,102,95,97,95,93,93,82,108,107,99,109,104,105,175,100,83", "endOffsets": "491,595,708,795,897,1019,1102,1182,1276,1372,1469,1565,1668,1764,1862,1958,2052,2146,2229,2338,2446,2546,2656,2761,2867,3043,3144,12384"}}]}]}