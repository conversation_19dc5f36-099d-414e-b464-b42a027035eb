"use client";

import React from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Palette } from 'lucide-react';

interface FinishSelectorProps {
  availableFinishes: string[];
  selectedFinish?: string;
  onFinishChange: (finish: string) => void;
  className?: string;
}

const FinishSelector: React.FC<FinishSelectorProps> = ({
  availableFinishes,
  selectedFinish,
  onFinishChange,
  className = ""
}) => {
  if (!availableFinishes || availableFinishes.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-2">
        <Palette className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-700">
          Available Finish Options:
        </span>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {availableFinishes.map((finish) => (
          <Button
            key={finish}
            variant={selectedFinish === finish ? "default" : "outline"}
            size="sm"
            onClick={() => onFinishChange(finish)}
            className="h-8 px-3 text-xs"
          >
            {finish}
          </Button>
        ))}
      </div>
      
      {selectedFinish && (
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-600">Selected:</span>
          <Badge variant="secondary" className="text-xs">
            {selectedFinish}
          </Badge>
        </div>
      )}
    </div>
  );
};

export default FinishSelector;
