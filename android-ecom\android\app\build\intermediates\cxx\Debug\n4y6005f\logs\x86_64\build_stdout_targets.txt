ninja: Entering directory `D:\Triumph\android-ecom\android\app\.cxx\Debug\n4y6005f\x86_64'
[0/2] Re-checking globbed directories...
[1/63] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/63] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[3/63] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[4/63] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp.o
[5/63] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[6/63] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[7/63] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o
[8/63] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[9/63] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o
[10/63] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[11/63] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o
[12/63] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o
[13/63] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[14/63] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
[15/63] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/rngesturehandler_codegenJSI-generated.cpp.o
[16/63] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o
[17/63] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o
[18/63] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o
[19/63] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp.o
[20/63] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[21/63] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[22/63] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[23/63] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[24/63] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[25/63] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[26/63] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[27/63] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[28/63] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[29/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f2310720124141018599056b8bc283de/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[30/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f5154aee4d0be51bbcee13765fb5df63/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp.o
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:16:69: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCSafeAreaProviderEventEmitter::onInsetsChange(OnInsetsChange $event) const {
                                                                    ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:34: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                 ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                  ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:18:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:21:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "top", $event.insets.top);
                                     ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:22:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "right", $event.insets.right);
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:23:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "bottom", $event.insets.bottom);
                                        ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:24:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "left", $event.insets.left);
                                      ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:25:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "insets", insets);
  ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:29:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "x", $event.frame.x);
                                  ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:30:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "y", $event.frame.y);
                                  ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:31:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "width", $event.frame.width);
                                      ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:32:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "height", $event.frame.height);
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:33:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "frame", frame);
  ^
D:/Triumph/android-ecom/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:35:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
15 warnings generated.
[31/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/681ec86e4c0f1554915986f37ebd32c7/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[32/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c250ed7362850ce2c5fd8ba13148937f/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp.o
[33/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/be3f2640dad0498eddbc41153fe70dce/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp.o
[34/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f5154aee4d0be51bbcee13765fb5df63/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp.o
[35/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f154327ccaf51013ddd920c4863922ca/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[36/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c250ed7362850ce2c5fd8ba13148937f/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp.o
[37/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f154327ccaf51013ddd920c4863922ca/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[38/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f154327ccaf51013ddd920c4863922ca/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[39/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/514353fe27f640a68c9005975b27fa8b/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp.o
[40/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4fe7d22b5ffc23b39a2e21d855cb1940/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[41/63] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6e237aa0a79c7fde022b820e94db3e86/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp.o
[42/63] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o
[43/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[44/63] Linking CXX shared library D:\Triumph\android-ecom\android\app\build\intermediates\cxx\Debug\n4y6005f\obj\x86_64\libreact_codegen_safeareacontext.so
[45/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4fe7d22b5ffc23b39a2e21d855cb1940/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[46/63] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o
[47/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f154327ccaf51013ddd920c4863922ca/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[48/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f154327ccaf51013ddd920c4863922ca/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[49/63] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o
[50/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6739b7bd133cfbaa08ae9d98c4ffa15c/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[51/63] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o
[52/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f7888d7eaf7ea1c5471c970d3b8a6580/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[53/63] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o
[54/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/13e4a8042b3212756d527c6d2adf2453/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[55/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6739b7bd133cfbaa08ae9d98c4ffa15c/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[56/63] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o
[57/63] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o
[58/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/13e4a8042b3212756d527c6d2adf2453/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:17:52: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onAppear(OnAppear $event) const {
                                                   ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:19:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:21:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:26:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onDisappear(OnDisappear $event) const {
                                                         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:28:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:30:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:35:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onDismissed(OnDismissed $event) const {
                                                         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:37:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:39:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:44:84: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
                                                                                   ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                            ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:46:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:48:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:53:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
                                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:55:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:57:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:62:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
                                                                 ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:64:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:66:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:71:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
                                                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:73:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
                                                  ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:75:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:80:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
                                                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:82:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
                                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
                                         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
                                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:86:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:91:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
                                                                 ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:93:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:95:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:100:86: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
                                                                                     ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:102:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:104:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:109:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged $event) const {
                                                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:110:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:110:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:111:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:112:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:112:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:113:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:113:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
                                          ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:114:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:122:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onAppear(OnAppear $event) const {
                                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:124:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:126:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:131:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onDisappear(OnDisappear $event) const {
                                                    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:133:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:135:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:140:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onDismissed(OnDismissed $event) const {
                                                    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:141:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:141:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:142:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:143:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:143:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:144:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:149:79: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
                                                                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:150:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:150:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                            ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:151:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:152:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:152:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:153:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:158:55: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
                                                      ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:160:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:162:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:167:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
                                                            ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:169:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:171:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:176:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
                                                                      ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:178:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:179:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:179:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
                                                  ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:180:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:185:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
                                                                      ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:186:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:186:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:187:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:188:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:188:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
                                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:189:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:189:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
                                         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:190:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:190:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
                                              ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:191:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:196:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
                                                            ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:198:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:200:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:205:81: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
                                                                                ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:207:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:209:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:214:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged $event) const {
                                                                      ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:215:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:215:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:216:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:217:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:217:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:218:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:218:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
                                          ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:219:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:225:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackHeaderConfigEventEmitter::onAttached(OnAttached $event) const {
                                                                   ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:227:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:229:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:234:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackHeaderConfigEventEmitter::onDetached(OnDetached $event) const {
                                                                   ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:236:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:238:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:244:78: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackEventEmitter::onFinishTransitioning(OnFinishTransitioning $event) const {
                                                                             ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:246:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:248:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:253:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchFocus(OnSearchFocus $event) const {
                                                           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:255:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:257:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:262:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchBlur(OnSearchBlur $event) const {
                                                         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:264:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:266:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:271:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchButtonPress(OnSearchButtonPress $event) const {
                                                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:272:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                      ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:272:56: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:273:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:274:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:274:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
                                          ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:275:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:280:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onCancelButtonPress(OnCancelButtonPress $event) const {
                                                                       ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:282:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:284:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:289:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onChangeText(OnChangeText $event) const {
                                                         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:290:32: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
                               ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:290:49: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:291:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:292:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
    ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:292:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
                                          ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:293:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:298:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onClose(OnClose $event) const {
                                               ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:300:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:302:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:307:46: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onOpen(OnOpen $event) const {
                                             ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:309:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Triumph/android-ecom/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:311:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
156 warnings generated.
[59/63] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f7888d7eaf7ea1c5471c970d3b8a6580/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[60/63] Linking CXX shared library D:\Triumph\android-ecom\android\app\build\intermediates\cxx\Debug\n4y6005f\obj\x86_64\libreact_codegen_rnscreens.so
[61/63] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[62/63] Building CXX object CMakeFiles/appmodules.dir/D_/Triumph/android-ecom/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[63/63] Linking CXX shared library D:\Triumph\android-ecom\android\app\build\intermediates\cxx\Debug\n4y6005f\obj\x86_64\libappmodules.so
