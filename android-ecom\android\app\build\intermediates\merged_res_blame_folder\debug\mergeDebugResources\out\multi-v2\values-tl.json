{"logs": [{"outputFile": "com.sohangpurh53.ecommtest.app-mergeDebugResources-62:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab468244bce13502a51b6cbb55b0c3ba\\transformed\\material-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,235,321,426,562,647,712,811,879,938,1027,1094,1157,1232,1300,1354,1474,1532,1594,1648,1723,1865,1955,2040,2155,2239,2322,2418,2485,2551,2625,2703,2794,2868,2947,3020,3092,3196,3269,3368,3468,3542,3617,3724,3776,3843,3934,4028,4090,4154,4217,4336,4438,4547,4650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "230,316,421,557,642,707,806,874,933,1022,1089,1152,1227,1295,1349,1469,1527,1589,1643,1718,1860,1950,2035,2150,2234,2317,2413,2480,2546,2620,2698,2789,2863,2942,3015,3087,3191,3264,3363,3463,3537,3612,3719,3771,3838,3929,4023,4085,4149,4212,4331,4433,4542,4645,4730"}, "to": {"startLines": "2,33,41,42,43,52,53,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3845,3950,4086,4958,5023,6793,7035,7094,7183,7250,7313,7388,7456,7510,7630,7688,7750,7804,7879,8021,8111,8196,8311,8395,8478,8574,8641,8707,8781,8859,8950,9024,9103,9176,9248,9352,9425,9524,9624,9698,9773,9880,9932,9999,10090,10184,10246,10310,10373,10492,10594,10703,10982", "endLines": "5,33,41,42,43,52,53,67,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,118", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "280,3106,3945,4081,4166,5018,5117,6856,7089,7178,7245,7308,7383,7451,7505,7625,7683,7745,7799,7874,8016,8106,8191,8306,8390,8473,8569,8636,8702,8776,8854,8945,9019,9098,9171,9243,9347,9420,9519,9619,9693,9768,9875,9927,9994,10085,10179,10241,10305,10368,10487,10589,10698,10801,11062"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\42358a9cae14349299c1c7a268311762\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,396,504,617,705,811,926,1006,1083,1174,1267,1362,1456,1556,1649,1744,1838,1929,2020,2104,2213,2323,2424,2534,2652,2760,2923,11240", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "391,499,612,700,806,921,1001,1078,1169,1262,1357,1451,1551,1644,1739,1833,1924,2015,2099,2208,2318,2419,2529,2647,2755,2918,3020,11320"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a65209923ef4068f1ffed3bedec62176\\transformed\\jetified-ui-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,290,387,489,579,661,753,845,929,1016,1102,1173,1256,1333,1408,1486,1552", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "199,285,382,484,574,656,748,840,924,1011,1097,1168,1251,1328,1403,1481,1547,1674"}, "to": {"startLines": "44,45,48,50,51,68,69,116,117,119,120,122,123,124,125,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4171,4270,4573,4766,4868,6861,6943,10806,10898,11067,11154,11325,11396,11479,11556,11732,11810,11876", "endColumns": "98,85,96,101,89,81,91,91,83,86,85,70,82,76,74,77,65,126", "endOffsets": "4265,4351,4665,4863,4953,6938,7030,10893,10977,11149,11235,11391,11474,11551,11626,11805,11871,11998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c6e705899487cb68772be9da92e21a0c\\transformed\\browser-1.6.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "47,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4470,5122,5227,5338", "endColumns": "102,104,110,104", "endOffsets": "4568,5222,5333,5438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7a0941004ac45c50b486dd320a2bfc96\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "34,35,36,37,38,39,40,126", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3111,3208,3310,3411,3508,3615,3723,11631", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3203,3305,3406,3503,3610,3718,3840,11727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\bf233ddec2dc68e89587520b037b44b2\\transformed\\jetified-foundation-release\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,91", "endOffsets": "136,228"}, "to": {"startLines": "130,131", "startColumns": "4,4", "startOffsets": "12003,12089", "endColumns": "85,91", "endOffsets": "12084,12176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\09fbd29eeff6f06226720f66e439d483\\transformed\\biometric-1.1.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,265,387,540,683,835,962,1100,1200,1337,1489", "endColumns": "113,95,121,152,142,151,126,137,99,136,151,125", "endOffsets": "164,260,382,535,678,830,957,1095,1195,1332,1484,1610"}, "to": {"startLines": "46,49,57,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4356,4670,5443,5565,5718,5861,6013,6140,6278,6378,6515,6667", "endColumns": "113,95,121,152,142,151,126,137,99,136,151,125", "endOffsets": "4465,4761,5560,5713,5856,6008,6135,6273,6373,6510,6662,6788"}}]}]}