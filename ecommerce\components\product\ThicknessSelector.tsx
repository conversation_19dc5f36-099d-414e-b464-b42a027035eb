"use client";

import React from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Layers } from 'lucide-react';

interface ThicknessSelectorProps {
  availableThicknesses: string[];
  selectedThickness?: string;
  onThicknessChange: (thickness: string) => void;
  className?: string;
}

const ThicknessSelector: React.FC<ThicknessSelectorProps> = ({
  availableThicknesses,
  selectedThickness,
  onThicknessChange,
  className = ""
}) => {
  if (!availableThicknesses || availableThicknesses.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-2">
        <Layers className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-700">
          Available Thickness Options:
        </span>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {availableThicknesses.map((thickness) => (
          <Button
            key={thickness}
            variant={selectedThickness === thickness ? "default" : "outline"}
            size="sm"
            onClick={() => onThicknessChange(thickness)}
            className="h-8 px-3 text-xs"
          >
            {thickness}
          </Button>
        ))}
      </div>
      
      {selectedThickness && (
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-600">Selected:</span>
          <Badge variant="secondary" className="text-xs">
            {selectedThickness}
          </Badge>
        </div>
      )}
    </div>
  );
};

export default ThicknessSelector;
